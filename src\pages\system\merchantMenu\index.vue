<template>
  <div class="table-box">
    <!-- table - 商家菜单列表  -->
    <custom-table
      :columns="columns"
      :data-source="paginatedData"
      :pagination="pagination"
      :loading="isLoading"
      @change="handleTableChange">
      <template #actions>
        <Button type="primary" @click="showAddModal"
          >新增
          <template #icon><PlusCircleOutlined /></template>
        </Button>
      </template>
    </custom-table>

    <!-- modal - 添加/修改弹窗  -->
    <add-edit-modal
      :visible="addEditModalVisible"
      :record="editingRecord"
      :menu-list="dataSource"
      :is-editing="isEditing"
      @update:visible="addEditModalVisible = $event"
      @submit="handleSubmit"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, h, computed } from 'vue';
import { Button, message, Tooltip } from 'woody-ui';
import { PlusCircleOutlined } from '@ant-design/icons-vue';
import CustomTable from '@/components/CustomTable/index.vue'; // 封装表格
import CustomActionCell from './components/CustomActionCell.vue'; // 封装操作
import AddEditModal from './components/AddEditModal.vue'; // 封装弹窗
import { addMenuService, getMenuListService } from '@/api/menu'; // 接口调用
import { MENU_TYPE, SHOW_FLAG } from '@/constants'; //自定义枚举

// ---------数据表相关-----------
const columns = ref([
  { key: 'menuName', title: '名称', dataIndex: 'menuName', width: 120, align: 'center' },
  {
    key: 'type',
    title: '类型',
    dataIndex: 'type',
    width: 120,
    customRender: ({ value }) => {
      return MENU_TYPE[value] || '-';
    },
  },
  {
    key: 'hidden',
    title: '是否隐藏',
    dataIndex: 'hidden',
    width: 120,
    customRender: ({ value }) => {
      return SHOW_FLAG[value] || '-';
    },
  },
  {
    key: 'relBusinessTypeName',
    title: '关联店铺类型',
    dataIndex: 'relBusinessTypeName',
    width: 120,
    ellipsis: true,
    customRender: ({ value }) => {
      return h(Tooltip, { title: value, placement: 'topLeft' }, () => h('span', value));
    },
  },
  {
    key: 'action',
    title: '操作',
    align: 'center',
    width: 150,
    fixed: 'right',
    isAction: true, // 标记为操作列
    customRender: ({ record }) => {
      return h(CustomActionCell, { record, onEdit: showAddModal, onDelete: getMenuList });
    },
  },
]);

const dataSource = ref([]);

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: dataSource.value.length,
});

// 计算属性：根据分页信息获取当前页的数据
const paginatedData = computed(() => {
  const { current, pageSize } = pagination.value;
  const start = (current - 1) * pageSize;
  const end = start + pageSize;
  return dataSource.value.slice(start, end);
});

// 接口获取店铺列表展示
const getMenuList = async () => {
  isLoading.value = true;
  const { current, pageSize } = pagination.value;
  const params = { page: current, size: pageSize };
  const res = await getMenuListService(params);
  isLoading.value = false;
  if (res.code === 0) {
    const { data } = res;
    dataSource.value = buildTree(data);
    pagination.value.total = dataSource.value.length;
  }
};

// 添加分页变化处理函数
const handleTableChange = (paginationInfo: any) => {
  pagination.value.current = paginationInfo.current;
  pagination.value.pageSize = paginationInfo.pageSize;
  // 如果需要后端分页，取消下面的注释
  // getMenuList();
};

// -----------辅助控制--------------
const isLoading = ref<boolean>(false);
const addEditModalVisible = ref(false);
const editingRecord = ref<any>(null);
const isEditing = ref(false);

const showAddModal = (record?: any) => {
  editingRecord.value = record;
  isEditing.value = !!record?.id;
  addEditModalVisible.value = true;
};

const handleSubmit = (values: any) => {
  values.parentId = values.parentId ?? 0;
  values.useScope = values.useScope.join(',');
  addMenuService(values).then((res) => {
    if (res.code === 0) {
      message.success(isEditing.value ? '修改成功' : '添加成功');
      getMenuList();
    }
  });
  addEditModalVisible.value = false;
};

// -----------处理函数--------------
interface TreeNode {
  id: number;
  parentId: number;
  permissionCode: string;
  type: number;
  menuName: string;
  permissionLevel: number;
  createTime: string;
  updateTime: string;
  menuBusinessList: any;
  relBusinessTypeName: any;
  useScope: number[];
  hidden: number;
  children?: TreeNode[];
}
const buildTree = (data: TreeNode[]): TreeNode[] => {
  const map = new Map<number, TreeNode>();
  const tree: TreeNode[] = [];

  // 将所有节点放入 map 中，键为 id，值为节点对象
  data.forEach((item) => {
    map.set(item.id, { ...item });
  });

  // 遍历所有节点，根据 parentId 将子节点添加到父节点的 children 数组中
  data.forEach((item) => {
    const node = map.get(item.id);
    if (!node) {
      console.warn(`Node with id ${item.id} not found in map`);
      return;
    }

    if (item.parentId !== 0) {
      const parent = map.get(item.parentId);
      if (parent) {
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(node);
      } else {
        console.warn(`Parent with id ${item.parentId} not found for item with id ${item.id}`);
      }
    } else {
      tree.push(node);
    }
  });

  // 移除空的 children 字段
  const removeEmptyChildren = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      if (node.children && node.children.length === 0) {
        delete node.children;
      } else if (node.children) {
        removeEmptyChildren(node.children);
      }
    });
  };

  removeEmptyChildren(tree);

  return tree;
};

// ----------生命周期处理-------------
onMounted(() => {
  getMenuList();
});
</script>

<style lang="less" scoped>
.table-box {
  height: calc(100vh - 103px); /* 设置固定高度 */
  // margin-top: 15px !important;
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  background: #fff;
  padding: 32px;
  margin-right: 16px;
  overflow: hidden; /* 防止内容溢出 */

  .table-operate-box {
    padding-bottom: 16px;
    text-align: right;
  }

  .table-operate-flex {
    display: flex;
    justify-content: flex-start;
  }
}
</style>


