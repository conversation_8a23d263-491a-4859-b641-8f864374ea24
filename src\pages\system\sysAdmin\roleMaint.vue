<template>
  <l-breadcrumb style="background: none; margin-top: 10px" :crumbs="crumbs" />
  <div style="padding-right: 15px">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-css">
      <div>
        <a-button type="primary" class="add-css" @click="addClick">新增</a-button>
      </div>
      <!-- 注意组件父元素的宽度 -->
      <div style="width: 100%" class="tdesign-demo-block-column-large tdesign-demo__table-affix">
        <a-table
          :key="tableKey"
          row-key="index"
          :data-source="roleData"
          :columns="columns"
          :row-class-name="rowClassName"
          :pagination="pagination"
          :scroll="{ x: 1000 }"
          @change="onPageChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 1 ? 'blue' : 'red'">
                {{ statusNameListMap[record.status].label }}
              </a-tag>
            </template>
            <template v-if="column.key === 'operation'">
              <a-button type="link" class="btn-css" @click="lookClick(record)">查看</a-button>
              <a-button type="link" class="btn-css" @click="editClick(record)">编辑</a-button>
              <a-popconfirm
                ok-text="确定"
                cancel-text="取消"
                :title="record.status === 1 ? '确认禁用吗？' : '确认启用吗？'"
                @confirm="disOrUpClick(record)"
              >
                <a-button type="link" class="btn-css">{{ record.status === 1 ? '禁用' : '启用' }}</a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
  <a-drawer
    :title="titleType"
    width="40%"
    placement="right"
    :visible="addVisible"
    :destroy-on-close="true"
    @close="onClose"
  >
    <div class="dialog-pad" style="width: 100%">
      <a-form
        ref="formRef"
        :rules="FORM_RULES"
        v-bind="layout"
        :model="formData"
        layout="horizontal"
        style="width: 100%"
      >
        <a-form-item label="角色名称" name="roleName">
          <a-input
            v-model:value="formData.roleName"
            placeholder="请输入内容"
            :maxlength="30"
            show-limit-number
            :disabled="isEdit"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入内容"
            maxlength="30"
            :disabled="isEdit"
            style="width: 100%"
          />
        </a-form-item>
        <!-- required-mark -->
        <a-form-item label="权限" name="authMenu">
          <div class="tree-flex mt10">
            <div v-if="newItems.length" style="width: 100%">
              <span>我店生活运营管理平台</span>
              <t-tree
                ref="tree"
                v-model="selectVal"
                class="tree-css"
                :data="newItems"
                :checkable="checkable"
                :check-strictly="checkStrictly"
                :value-mode="valueMode"
                :keys="fieldNames"
                :destroy-on-close="true"
                :disabled="isEdit"
                hover
                expand-all
                @change="onTreeChange"
              />
            </div>
            <div v-if="jicaiItems.length" style="width: 100%">
              <span>集采运营平台</span>
              <t-tree
                ref="jiCaiTree"
                v-model="selectJicaiVal"
                class="tree-css"
                :data="jicaiItems"
                :checkable="checkable"
                :check-strictly="checkStrictly"
                :value-mode="valueMode"
                :keys="fieldNames"
                :destroy-on-close="true"
                :disabled="isEdit"
                hover
                expand-all
                @change="onJicaiTreeChange"
              />
            </div>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <div style="text-align: right">
        <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
        <a-button v-if="!isEdit" type="primary" @click="clickSubmit">确定</a-button>
      </div>
    </template>
  </a-drawer>
</template>
<script setup lang="tsx">
import { ref, watch, reactive, onMounted, toRaw } from 'vue';
import { MessagePlugin, FormInstanceFunctions, FormProps, TreeProps, TreeInstanceFunctions } from 'tdesign-vue-next';
import { CheckCircleFilledIcon, CloseCircleFilledIcon } from 'tdesign-icons-vue-next';
import { useRoute } from 'vue-router';
import {
  getPlatRoleList,
  addPlatRole,
  getPlatRoleById,
  banRolePlatUser,
  getPlatMenuTreeList,
  updatePlatRole,
  getCurrentAssignMenus,
} from '@/api/system';
// import roleSearch from './components/roleSearch.vue';
import SearchAntd from '@/components/SearchAntd/index.vue';
import LBreadcrumb from '@/components/Breadcrumb.vue';

const route = useRoute();
const addVisible = ref(false);
const titleType = ref('新增');
const formList = ref([
  {
    label: '角色名称',
    name: 'roleName',
    type: 'input', // 输入框
    span: 6,
  },
]);
const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};

const formRef = ref<FormInstanceFunctions>(null);
const fieldNames = {
  label: 'name', // 节点标签字段
  value: 'menuId', // 节点值字段
  children: 'list', // 子节点字段
};
const tree = ref<TreeInstanceFunctions>();
const oldTree = ref<TreeInstanceFunctions>();
const jiCaiTree = ref<TreeInstanceFunctions>();

const valueMode = ref<TreeProps['valueMode']>('all');
const checkable = ref(true);
const checkStrictly = ref(false);

// const items = ref<TreeProps['data']>(treeItems);
const items = ref([]);

const newItems = ref([]);
const oldItems = ref([]);
const jicaiItems = ref([]);
const selectVal = ref([]);
const selectOldVal = ref([]);
const selectJicaiVal = ref([]);
const tableKey = ref(0);
const crumbs = ref([]);
// 动态改变面包屑
const getBreadcrumb = () => {
  crumbs.value = [
    {
      title: '系统',
      path: '',
      disabled: true,
      id: 1,
    },
    {
      title: route.query.teamAdminId ? '团队管理' : '管理员管理',
      path: route.query.teamAdminId ? '' : '/teamManangment/teamList',
      disabled: false,
      id: 2,
    },
    {
      title: '团队列表',
      path: '/teamManangment/teamList',
      disabled: false,
      id: 3,
    },
    {
      title: '角色管理',
      path: route.query.teamAdminId ? '' : '/adminManangment/roleManangment',
      disabled: false,
      id: 4,
    },
  ];
  if (!route.query.teamAdminId) crumbs.value.splice(2, 1); // 从团队管理切到管理员管理，需要删掉团队管理面包屑
};
// 搜索回调
const handleSearch = (param) => {
  const { roleName } = param;
  pagination.value.defaultCurrent = 1;
  pagination.value.defaultPageSize = 10;
  serachObj.roleName = roleName;
  getRoleList();
};

// 监听选中的变化
const onTreeChange = (checkedKeys: any[]) => {
  const parentIds = [];
  checkedKeys.forEach((key) => {
    // 使用 getParents 方法获取该节点的所有父节点
    for (const i of tree.value.getParents(key)) {
      parentIds.push(i.value);
    }
  });
  const arr = [...new Set([...selectVal.value, ...parentIds])];
  formData.menuIds = arr;
};

const onOldTreeChange = (checkedKeys: any[]) => {
  const parentIds = [];
  checkedKeys.forEach((key) => {
    // 使用 getParents 方法获取该节点的所有父节点
    for (const i of oldTree.value.getParents(key)) {
      parentIds.push(i.value);
    }
  });
  const arr = [...new Set([...selectOldVal.value, ...parentIds])];
  formData.oldMenuIds = arr;
};

const onJicaiTreeChange = (checkedKeys: any[]) => {
  const parentIds = [];
  checkedKeys.forEach((key) => {
    // 使用 getParents 方法获取该节点的所有父节点
    for (const i of jiCaiTree.value.getParents(key)) {
      parentIds.push(i.value);
    }
  });
  const arr = [...new Set([...selectJicaiVal.value, ...parentIds])];
  formData.jicaiMenuIds = arr;
};

const FORM_RULES = {
  roleName: [
    {
      required: true,
      message: '角色名称必填',
    },
  ],
  authMenu: [
    {
      required: true,
      message: '权限必选',
      trigger: 'change',
    },
  ],
};
const formData: FormProps['data'] = reactive({
  roleName: '',
  remark: '',
  menuIds: [],
  oldMenuIds: [],
  jicaiMenuIds: [],
  authMenu: [],
});

const statusNameListMap = {
  1: { label: '启用', theme: 'success', icon: <CheckCircleFilledIcon /> },
  0: { label: '禁用', theme: 'danger', icon: <CloseCircleFilledIcon /> },
};

const columns = [
  {
    title: '角色ID',
    dataIndex: 'roleId',
    key: 'roleId',
    fixed: 'left',
    align: 'left',
    width: 200,
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
    key: 'roleName',
    align: 'left',
    width: 200,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    align: 'left',
    width: 200,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    align: 'left',
    width: 200,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    key: 'createUser',
    align: 'left',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    align: 'left',
    width: 200,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    align: 'left',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    align: 'left',
    width: 260,
  },
];

const roleData = ref([]);
const roleId = ref(null);
const isEdit = ref(false);
const serachObj = reactive({
  roleName: '',
});

// type 可选值：foot 和 body
function rowClassName({ type }) {
  if (type === 'foot') return 't-tdesign__custom-footer-tr';
  return 't-tdesign__custom-body-tr';
}

const pagination = ref({ defaultCurrent: 1, defaultPageSize: 10, total: 0 });

// 新增
const addClick = () => {
  titleType.value = '新增';
  isEdit.value = false;
  formData.roleName = '';
  formData.remark = '';
  formData.menuIds = [];
  formData.oldMenuIds = [];
  formData.jicaiMenuIds = [];
  roleId.value = '';
  selectVal.value = [];
  selectOldVal.value = [];
  selectJicaiVal.value = [];
  addVisible.value = true;
};
// 查看
const lookClick = (row) => {
  titleType.value = '详情';
  isEdit.value = true;
  getDetail(row);
};
// 编辑
const editClick = (row) => {
  titleType.value = '编辑';
  isEdit.value = false;
  getDetail(row);
};
// 禁止或启动
const disOrUpClick = async (row) => {
  const { teamId } = route.query;
  const { roleId, status } = row;
  const params = {
    status: status === 1 ? 0 : 1,
    roleId,
    teamId,
  };
  const res = await banRolePlatUser(params);
  if (res.code === 0) {
    MessagePlugin.success(status === 0 ? '已启用' : '已禁用');
    getRoleList();
  }
};

// 添加确定
const clickSubmit = () => {
  console.log(formData, '查看数据');
  const { remark, roleName, menuIds, oldMenuIds, jicaiMenuIds } = formData;

  const newMenu = menuIds.map((v) => Number(v));
  const oldMenu = oldMenuIds.map((v) => Number(v));
  const jicaiMenu = jicaiMenuIds.map((v) => Number(v));
  console.log(menuIds, oldMenuIds, '查看数据已选权限数据');
  formData.authMenu = [...newMenu, ...oldMenu, ...jicaiMenu];
  formRef.value
    .validate()
    .then(async () => {
      const { teamAdminId, teamId } = route.query;
      const params = {
        roleName,
        remark,
        menuIds: formData.authMenu,
        roleId: roleId.value,
        teamAdminId,
        teamId,
      };
      const resq = titleType.value === '新增' ? await addPlatRole(params) : await updatePlatRole(params);
      if (resq.code === 0) {
        MessagePlugin.success(titleType.value === '新增' ? '新增成功' : '编辑成功');
        addVisible.value = false;
        getRoleList();
      }
    })
    .catch((error) => {
      console.log('error', error);
    });
};
const onClose = () => {
  addVisible.value = false;
};

// 获取详情
const getDetail = async (row) => {
  const res = await getPlatRoleById({ roleId: row.roleId });
  if (res.code === 0) {
    addVisible.value = true;
    const { roleName, remark, menuIds, npMenuIds, jicaiMenuIds } = res.data;
    formData.roleName = roleName;
    formData.remark = remark;
    formData.menuIds = npMenuIds;
    formData.oldMenuIds = menuIds;
    formData.jicaiMenuIds = jicaiMenuIds;
    roleId.value = row.roleId;
    // 获取最底层的 menuId
    const deepestMenuId = findBottomLevelMenuIds(toRaw(newItems.value), npMenuIds);
    selectVal.value = deepestMenuId;
    const deepestOldMenuId = findBottomLevelMenuIds(toRaw(oldItems.value), menuIds);
    selectOldVal.value = deepestOldMenuId;
    const deepestJicaiMenuId = findBottomLevelMenuIds(toRaw(jicaiItems.value), jicaiMenuIds);
    selectJicaiVal.value = deepestJicaiMenuId;
  }
};
// 递归查找最底层的菜单ID
function findBottomLevelMenuIds(menuList, selectedIds) {
  const result = [];

  // 遍历当前的菜单列表
  for (const menu of menuList) {
    // 如果当前节点的 menuId 在 selectedIds 中，继续递归
    if (selectedIds.includes(Number(menu.menuId))) {
      // 如果当前节点有子节点，继续检查它们
      if (menu.list && menu.list.length > 0) {
        result.push(...findBottomLevelMenuIds(menu.list, selectedIds));
      } else {
        // 如果当前节点没有子节点（即最底层节点），加入结果
        result.push(menu.menuId);
      }
    }
  }

  return result;
}

const getMenuList = async () => {
  if (!items.value.length) {
    const { teamAdminId } = route.query;
    const res = await getCurrentAssignMenus({ teamAdminId });
    if (res.code === 0) {
      newItems.value = res.data.npPlatMenu;
      oldItems.value = res.data.platMenu;
      jicaiItems.value = res.data.jicaiMenu;
    }
  }
};

// 分页
const onPageChange = (e) => {
  pagination.value.defaultCurrent = e.current;
  pagination.value.defaultPageSize = e.pageSize;
  getRoleList();
};
watch(
  route, // 监听 route 对象
  (newRoute) => {
    getBreadcrumb();
    getRoleList();
  },
  // { immediate: true, deep: true }, // 初始化时立即执行一次
);

const getRoleList = async () => {
  const { teamAdminId, teamId } = route.query;
  const parmas = {
    roleName: serachObj.roleName,
    page: pagination.value.defaultCurrent,
    size: pagination.value.defaultPageSize,
    teamAdminId,
    teamId,
  };
  const res = await getPlatRoleList(parmas);
  if (res.code === 0) {
    const { total, records } = res.data;
    pagination.value.total = total;
    roleData.value = records;
  }
};
onMounted(() => {
  getRoleList();
  getBreadcrumb();
  getMenuList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  margin-top: 16px;
}
.add-css {
  float: right;
  margin-bottom: 10px;
}
// .btn-css {
//   color: #167fff;
//   cursor: pointer;
// }
.tree-css {
  width: 100%;
  margin-top: 3px;
}
.tree-flex {
  display: flex;
  flex-direction: column;
  width: 100%;
}
</style>
