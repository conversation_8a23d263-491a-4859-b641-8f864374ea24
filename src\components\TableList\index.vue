<template>
  <t-enhanced-table
    v-if="isEnhanced"
    :row-key="rowKey"
    :row-class-name="(params) => addRowClass(params)"
    :data="tableData"
    :columns="columns"
    :tree="treeConfig"
    :cell-empty-content="showEmpty"
    :loading="loading"
    pagination-affixed-bottom
    :header-affixed-top="{ offsetTop: 56 }"
    :table-layout="tableLayout"
    @expanded-tree-nodes-change="expandedTreeNodesChange"
    @page-change="onPageChange"
  >
    <template #empty>
      <div>
        <no-data />
      </div>
    </template>
    <template v-for="(slot, name) in $slots" :key="name" #[name]="{ row }">
      <slot :name="name" :row="row"></slot>
    </template>
  </t-enhanced-table>
  <t-table
    v-else
    :row-key="rowKey"
    :data="tableData"
    :columns="columns"
    :max-height="maxHeight"
    :selected-row-keys="selectedRowKeys"
    :pagination="pagination"
    :cell-empty-content="showEmpty"
    :loading="loading"
    pagination-affixed-bottom
    :header-affixed-top="{ offsetTop: 56 }"
    :table-layout="tableLayout"
    @select-change="handleSelectChange"
    @page-change="onPageChange"
  >
    <template #empty>
      <div>
        <no-data />
      </div>
    </template>
    <template v-for="(slot, name) in $slots" :key="name" #[name]="{ row }">
      <slot :name="name" :row="row"></slot>
    </template>
  </t-table>
</template>

<script setup>
import { nextTick, reactive, ref, watch } from 'vue';
import noData from '@/components/Nodata/index.vue';
import { isObj } from '@/utils';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  isEnhanced: {
    type: Boolean,
    default: false,
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  excluedsValues: {
    type: Array,
    default: () => [],
  },
  maxHeight: {
    type: [Number, String],
    default: '',
  },
  treeConfig: {
    type: Object,
    default: () => ({}),
  },
  pagination: {
    type: Object,
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
    }),
  },
});

const emits = defineEmits(['onPageChange', 'onSelectChange']);

const showEmpty = (e, col) => {
  const str = col.col.title ? '-' : '';
  return str;
};

const expandedIndexMap = reactive(new Map());
const selectedRowKeys = ref([]);
const tableLayout = ref('fixed');

// 表格书展开/收起时的逻辑
const expandedTreeNodesChange = (nodes, options) => {
  if (isObj(options)) {
    if (options.type === 'expand') {
      expandedIndexMap.set(options.rowState?.id, true);
    } else {
      expandedIndexMap.delete(options.rowState?.id);
    }
  }
};

// 给行添加样式的逻辑
const addRowClass = (params) => {
  return expandedIndexMap.has(params.row?.id) ? 'expanded-row' : '';
};

// 分页逻辑
const onPageChange = (pageInfo) => {
  emits('onPageChange', pageInfo);
};

// 选中逻辑
const handleSelectChange = (value, ctx, isCall = () => false) => {
  const result = isCall();
  if (result) {
    selectedRowKeys.value = result;
  } else {
    selectedRowKeys.value = value;
    emits('onSelectChange', selectedRowKeys.value, ctx);
  }
};

// 清空选中
const handleClearSelectedRowKeys = () => {
  selectedRowKeys.value = [];
  emits('onSelectChange', selectedRowKeys.value);
};

// 选中全部
const allSelectChange = (isAllPage = false) => {
  nextTick(() => {
    selectedRowKeys.value = props.tableData.reduce((prev, current) => {
      if (!props.excluedsValues.includes(current[props.rowKey])) {
        // 全选状态下，过滤掉不需要的项才能添加
        prev.push(current[props.rowKey]);
      }
      return prev;
    }, []);
    if (!isAllPage) {
      // 如果全部选择为跨页都需要选择的逻辑，此时不用将选中的值传出去，可跟后端约定好，通过字段标识来实现
      emits('onSelectChange', selectedRowKeys.value);
    }
  });
};

// 监听传进来的 columns
watch(
  () => props.columns,
  (newValue) => {
    if (Array.isArray(newValue) && 0 in newValue) {
      newValue.forEach((item) => {
        if (item.colKey === 'operate') {
          item.fixed = 'right';
        }
      });
    }
  },
  {
    immediate: true,
  },
);

// 全局抛出清空选中方法
defineExpose({ selectedRowKeys, handleSelectChange, handleClearSelectedRowKeys, allSelectChange });
</script>

<style lang="less" scoped>
.t-table {
  color: @table-td-color;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  overflow: hidden;
  &.t-table__content--scrollable-to-right {
    :deep(.t-table__cell--fixed-right-first) {
      &::after {
        border-left: 5px solid @table-boder-color;
      }
    }
  }
  :deep(.t-table__header) {
    th {
      background-color: @table-th-bg !important;
      color: @table-th-color;
      font-weight: 400;
    }
  }
  :deep(.t-table__body) {
    tr {
      background-color: #fff !important;
    }
    td {
      border-bottom-color: @table-boder-color;
      // &.operation {
      //   border-left: 5px solid @table-boder-color;
      // }
    }
    .expanded-row {
      .t-table__tree-op-icon {
        background-image: url('@/assets/images/tree-closed-icon.png');
      }
    }
    .t-table__tree-col {
      display: block;
    }
    .t-table__tree-op-icon {
      width: 16px;
      height: 16px;
      background-image: url('@/assets/images/tree-open-icon.png');
      background-size: 100% 100%;
      display: block;
      svg {
        display: none;
      }
    }
  }
  :deep(.t-table__pagination) {
    padding-top: 28px;
    .t-pagination {
      .t-pagination__jump {
        margin-left: 16px;
      }
    }
  }
}
</style>
