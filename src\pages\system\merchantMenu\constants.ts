// 列表字段
export const SHOP_MANAGE_COLUMNS = [
  {
    title: "店铺名称",
    key: "shopName",
    dataIndex: "shopName",
  },
  {
    title: "创建时间",
    key: "createTime",
    dataIndex: "createTime",
  },
  {
    title: "店铺状态",
    key: "shopStatus",
    dataIndex: "shopStatus",
  },
  {
    title: "店铺类型",
    key: "shopBusinessTypeName",
    dataIndex: "shopBusinessTypeName",
  },
  {
    title: "操作",
    key: "operation",
  },
];

export const ROW_KEY = "id";

// 店铺状态（后续要考虑是否全局使用？提炼作用域）
// -1:已删除 0: 停业中 1:营业中 2:平台下线 3.平台下线待审核 4.开店申请中 5.开店申请待审核
export const SHOP_STATUS = [
  {
    label: "已删除",
    value: -1,
  },
  {
    label: "停业中",
    value: 0,
  },
  {
    label: "营业中",
    value: 1,
  },
  {
    label: "平台下线",
    value: 2,
  },
  {
    label: "平台下线待审核",
    value: 3,
  },
  {
    label: "开店申请中",
    value: 4,
  },
  {
    label: "开店申请待审核",
    value: 5,
  },
];

// 展示列表分页
export const FROM_PAGE = {
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: () => {
    return `共${FROM_PAGE.total}条`;
  },
};

// 绑定生活圈店铺 分页
export const MODE_PAGE = {
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: () => {
    return `共${MODE_PAGE.total}条`;
  },
};

// 操作日志 分页
export const LOGHISTORY_PAGE = {
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: () => {
    return `共${LOGHISTORY_PAGE.total}条`;
  },
};
