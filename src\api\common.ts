import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';

const api = '/life-platform-dashboard';
export interface Response<T> {
  records: any;
  [x: string]: any;
  code: number;
  message: string;
  data: T;
}
export interface Pagination {
  current?: number;
  size: number;
  total?: number;
  pages?: number;
  page?: number;
}
export interface PaginationResponse<T> {
  [x: string]: any;
  pages: number;
  total: number;
  records: T;
}

// 图片上传
export const qiNiuYunToken = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/qiniuyun-upload/temporary-token`,
    data: {
      bizType: params.bizType,
      resourceType: params.resourceType,
      source: 'new_life_plat',
    },
    tokenProvider,
  });
export const uploadImg = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `/life-automatic-merchant/shop/image/upload`,
    data: params,
    tokenProvider,
    headers: { clienttype: 'PLATFORM' },
  });

export const uploadQiniu = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `https://upload.qiniup.com/`,
    data: params,
    tokenProvider,
    headers: { clienttype: 'PLATFORM' },
  });

// 查询分类
export const queryCategory = () =>
  request<Response<any>>({
    method: 'POST',
    path: `/life-platform-dashboard/platform/prod/newCategoryModelAll`,
  });
