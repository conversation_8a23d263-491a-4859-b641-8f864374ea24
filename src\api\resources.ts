import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';
import { Response, PaginationResponse } from './common';

const api = '/life-platform-dashboard';

// 查素材树
export const queryGroup = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'GET',
    path: `${api}/fileResourceGroup/group/query?flatFlag=${params.flatFlag}`,
    data: params,
    tokenProvider,
    headers: { 'X-Accept-Version': 'wx1' },
  });
// 素材列表查询
export const queryByPage = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<PaginationResponse<any>>>({
    method: 'POST',
    path: `${api}/fileResourceGroup/page-search`,
    data: params,
    tokenProvider,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 对应素材数量查询
export const countQuery = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'GET',
    path: `${api}/fileResourceGroup/group/materialCnt?groupId=${params.groupId}`,
    data: params,
    tokenProvider,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 批量修改素材的所在分组
export const modifyGroup = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'PUT',
    path: `${api}/fileResourceGroup/group/bind/batch`,
    data: params,
    tokenProvider,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 素材保存
export const saveMaterial = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/fileResourceGroup/save`,
    data: params,
    tokenProvider,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 素材分组保存
export const saveGroup = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/fileResourceGroup/group/save`,
    data: params,
    tokenProvider,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 图片上传
export const qiNiuYunToken = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/qiniuyun-upload/temporary-token`,
    data: {
      bizType: params.bizType,
      resourceType: 'image',
      source: 'new_life_plat',
    },
    tokenProvider,
  });
export const uploadImg = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `/life-automatic-merchant/shop/image/upload`,
    data: params,
    tokenProvider,
    headers: { clienttype: 'PLATFORM' },
  });
