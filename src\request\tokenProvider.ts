
import { refreshToken } from '@/api/login';

type AccessToken = string | null;
type RefreshToken = string | null;
export interface TokenProvider {
  accessToken?: AccessToken;
  refreshToken?: RefreshToken;
  refresh?: () => Promise<string | undefined>;
  customizedError?: any;
}

export class NoTokenRefresherError {
  toString() {
    return 'No token refresher initialized.';
  }
}

export const createTokenStore = () => {
  let accessToken: AccessToken = null;
  let refreshToken: RefreshToken = null;
  const setToken = (token: any) => {
    accessToken = token;
  };
  const getToken = () => accessToken as AccessToken;
  const setRefreshToken = (token: AccessToken) => {
    refreshToken = token;
  };
  const getRefreshToken = () => refreshToken as AccessToken;
  return { getToken, setToken, setRefreshToken, getRefreshToken };
};

export const createTokenRefresher = () => {
  return {
    init: (tokenRefresher: () => Promise<string>) => {
      return tokenRefresher;
    },
    refresh: async () => {
      if (tokenStore.getRefreshToken()) {
        const res = await refreshToken({
          refreshToken: tokenStore.getRefreshToken(),
        });
        if (res.code === 0) {
          return res.data;
        }

        // refresh 过期等原因导致 refreshToken 获取失败时，强制刷新跳回登录页面，进行重新登录
        localStorage.removeItem('user-auth');
        localStorage.removeItem('menuList');

        window.location.reload();
      }
      return Promise.resolve('');
    },
  };
};

export const tokenStore = createTokenStore();
export const tokenRefresher = createTokenRefresher();
const userAuth = JSON.parse(localStorage.getItem('user-auth')) || '';
const Token = userAuth ? userAuth.accessToken : '';
const refToken = userAuth ? userAuth.refreshToken : '';

export const getDefaultTokenProvider: () => TokenProvider = () => ({
  accessToken: Token || tokenStore.getToken(),
  refreshToken: refToken || tokenStore.getRefreshToken(),
  refresh: () =>
    tokenRefresher.refresh().then((token: any) => {
      tokenStore.setToken(token?.accessToken);
      tokenStore.setRefreshToken(token?.refreshToken);
      return token?.accessToken;
    }),
});
