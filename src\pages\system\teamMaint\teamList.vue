<template>
  <l-breadcrumb style="background: none; margin-top: 10px" :crumbs="crumbs" />
  <div class="main-css">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-css">
      <div>
        <a-button type="primary" class="add-css" @click="addClick">新增</a-button>
      </div>
      <!-- 注意组件父元素的宽度 -->
      <div style="width: 100%" class="tdesign-demo-block-column-large tdesign-demo__table-affix">
        <a-table
          :key="tableKey"
          row-key="index"
          :data-source="teamData"
          :columns="columns"
          :pagination="pagination"
          :scroll="{ x: 1000 }"
          @change="onPageChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operation'">
              <a-button type="link" class="btn-css" @click="lookdetail(record)">查看</a-button>
              <a-button type="link" class="btn-css" @click="editClick(record)">编辑</a-button>
              <a-button type="link" class="btn-css" @click="goToUrl(record, 'count')">成员</a-button>
              <a-button type="link" class="btn-css" @click="goToUrl(record, 'role')">角色</a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>

  <a-modal
    :title="titleType"
    width="30%"
    :close-on-overlay-click="true"
    :visible="addVisible"
    :on-close="onClose"
    :destroy-on-close="true"
    @ok="clickSubmit"
    @cancel="addVisible = false"
  >
    <div class="dialog-pad mt20">
      <a-form ref="formRef" :rules="FORM_RULES" :model="formData" direction="vertical">
        <a-form-item label="团队名称" name="teamName">
          <a-input
            v-model:value="formData.teamName"
            placeholder="请输入内容"
            :maxlength="30"
            show-limit-number
            :disabled="isDis"
          />
        </a-form-item>
      </a-form>
    </div>
    <template v-if="isDis" #footer>
      <span></span>
    </template>
  </a-modal>
</template>
<script setup lang="tsx">
import { ref, watch, h, computed, reactive, onMounted } from 'vue';
import { MessagePlugin, TableProps, FormProps, FormInstanceFunctions } from 'tdesign-vue-next';
import { useRouter, useRoute } from 'vue-router';
import { getPlatTeamListPage, addPlatTeam, updatePlatTeam } from '@/api/system';
import SearchAntd from '@/components/SearchAntd/index.vue';
import LBreadcrumb from '@/components/Breadcrumb.vue';

const teamId = ref(null);
const isEdit = ref(false);
const isDis = ref(false);
const titleType = computed(() => (isEdit.value ? '详情' : '新增'));
const serachObj = reactive({
  teamName: '',
});

const crumbs = ref([]);
const formList = ref([
  {
    label: '团队名称',
    name: 'teamName',
    type: 'input', // 输入框
    span: 6,
  },
]);
// 动态改变面包屑
const getBreadcrumb = () => {
  crumbs.value = [
    {
      title: '系统',
      path: '',
      disabled: true,
      id: 1,
    },
    {
      title: '团队管理',
      path: '/teamManangment/teamList',
      disabled: false,
      id: 2,
    },
    {
      title: '团队列表',
      path: '/teamManangment/teamList',
      disabled: false,
      id: 3,
    },
  ];
};

const router = useRouter();
const route = useRoute();
const addVisible = ref(false);
const FORM_RULES: FormProps['rules'] = {
  teamName: [
    {
      required: true,
      message: '团队名称必填',
    },
  ],
};
const formData: FormProps['data'] = reactive({
  teamName: '',
});
const formRef = ref<FormInstanceFunctions>(null);

const onClose = () => {
  addVisible.value = false;
  setTimeout(() => {
    formData.teamName = '';
  });
};

// 新增/编辑--确定
const clickSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      const params = {
        teamName: formData.teamName,
        teamId: teamId.value,
      };
      const res = titleType.value === '新增' ? await addPlatTeam(params) : await updatePlatTeam(params);
      if (res.code === 0) {
        MessagePlugin.success(titleType.value === '新增' ? '新增成功' : '编辑成功');
        addVisible.value = false;
        getList();
      }
    })
    .catch((error) => {
      console.log('error', error);
    });
};

const columns = [
  {
    title: '团队ID',
    dataIndex: 'teamId',
    key: 'teamId',
    fixed: 'left',
    align: 'left',
    width: 200,
  },
  {
    title: '团队名称',
    dataIndex: 'teamName',
    key: 'teamName',
    align: 'left',
    width: 200,
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
    align: 'left',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    align: 'left',
    width: 200,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    align: 'left',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'left',
    width: 260,
  },
];

const teamData = ref([]);
const handleSearch = (param) => {
  console.log(param, 'pram');
  const { teamName } = param;
  pagination.value.defaultCurrent = 1;
  pagination.value.defaultPageSize = 10;
  serachObj.teamName = teamName;
  getList();
};
const tableKey = ref(0);

const pagination = ref({ defaultCurrent: 1, defaultPageSize: 10, total: 0 });

const searchClick = (data) => {
  tableKey.value = Math.random();
  pagination.value.defaultCurrent = 1;
  pagination.value.defaultPageSize = 10;
  serachObj.teamName = data;
  getList();
};
// 新增
const addClick = () => {
  formData.teamName = '';
  isEdit.value = false;
  isDis.value = false;
  addVisible.value = true;
};

// 编辑
const editClick = (row) => {
  isEdit.value = true;
  isDis.value = false;
  teamId.value = row.teamId;
  formData.teamName = row.teamName;
  addVisible.value = true;
};

// 跳成员列表页面
const goToUrl = (row, type) => {
  router.push({
    path: type === 'count' ? '/adminManangment/countManangment' : '/adminManangment/roleManangment',
    query: { teamAdminId: row.teamAdminId, teamId: row.teamId },
  });
};

// 列表查询
const getList = async () => {
  const params = {
    teamName: serachObj.teamName,
    page: pagination.value.defaultCurrent,
    size: pagination.value.defaultPageSize,
  };
  const res = await getPlatTeamListPage(params);
  if (res.code === 0) {
    const { total, records } = res.data;
    teamData.value = records;
    pagination.value.total = total;
  }
};

// 分页
const onPageChange = (e) => {
  pagination.value.defaultCurrent = e.current;
  pagination.value.defaultPageSize = e.pageSize;
  getList();
};

// 查看详情
const lookdetail = (row) => {
  formData.teamName = row.teamName;
  isDis.value = true;
  isEdit.value = true;
  addVisible.value = true;
};

onMounted(() => {
  getBreadcrumb();
  getList();
});
</script>
<style lang="less" scoped>
.main-css {
  padding-right: 15px;
  margin-bottom: 70px;
  overflow: auto;
}
.table-css {
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  margin-top: 16px;
}
.add-css {
  float: right;
  margin-bottom: 10px;
}
// .btn-css {
//   color: #167fff;
//   cursor: pointer;
// }
</style>
