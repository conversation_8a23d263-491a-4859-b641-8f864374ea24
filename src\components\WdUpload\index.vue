<template>
  <a-upload
    v-model="props.fileList"
    name="file"
    list-type="picture-card"
    class="avatar-uploader"
    :accept="accept"
    :custom-request="customUpload"
    :multiple="props.multiple"
    :default-file-list="defaultFileList"
    :before-upload="beforeUpload"
    @change="handleChange"
    @preview="handlePreview"
    @remove="handleDelete"
    :max-count="props.maxCount"
    :key="Math.random()"
  >
    <div v-if="props.fileList.length < props.maxCount">
      <loading-outlined v-if="loading"></loading-outlined>
      <plus-outlined v-else></plus-outlined>
      <div class="ant-upload-text">{{ props.btnText }}</div>
    </div>
  </a-upload>

  <a-modal :open="previewVisible" :title="previewTitle" :footer="null" centered :zIndex="10000" @cancel="handleCancel">
    <img alt="example" style="width: 100%" :src="previewImage" />
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { message } from 'woody-ui';
import type { UploadChangeParam, UploadProps } from 'woody-ui';
import axios from 'axios';
import { qiNiuYunToken } from '@/api/common';
import Compressor from 'compressorjs';

const props = defineProps({
  resourceType: {
    type: String,
    default: 'image',
  },
  bizType: {
    type: String,
    default: '',
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  maxWidth: {
    type: Number,
    default: 10000,
  },
  fileList: {
    type: Array,
    default: () => [],
  },
  accept: {
    type: String,
    default: '',
  },
  btnText: {
    type: String,
    default: '上传',
  },
  fileSize: {
    type: Number,
    default: 1.8,
  },
  maxCount: {
    type: Number,
    default: 100,
  },
});
const emits = defineEmits(['getUrlList', 'before-upload']);

const fileList = ref<any>([]);
const defaultFileList = ref<any>([]);

function getBase64(img: Blob, callback: (base64Url: string) => void) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
}
function getBase642(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
const loading = ref<boolean>(false);

const tokenData = ref({
  agreement: '',
  domain: '',
  path: '',
  token: '',
});
const obj = ref({
  imageWidth: '',
  imageHeight: '',
});

// 上传前看图片信息
const beforeUpload = (file) => {
  console.log(file, 'beforeUpload');

  return new Promise((resolve, reject) => {
    const imageReader = new FileReader();
    imageReader.readAsDataURL(file);
    imageReader.addEventListener('loadend', (e) => {
      const imageSrc = e.target.result;
      calculateImageSize(imageSrc).then((res: any) => {
        console.log(res, 'calculateImageSize');

        // const maxSizeInMB = props.fileSize;
        // if (props.maxWidth && res.imageWidth > props.maxWidth) {
        //   MessagePlugin.error(`图片最大宽度为${props.maxWidth}px！`);
        //   return;
        // }
        // if (file.size > maxSizeInMB * 1024 * 1024) {
        //   MessagePlugin.error(`文件大小不能超过 ${maxSizeInMB}MB`);
        //   return;
        // }
        // if(fileList.value.length > props.maxCount){
        //   MessagePlugin.error(`文件上传不能超过 ${props.maxCount}张`);
        //   return;
        // }

        return resolve(file);

        // // 图片压缩
        // if (file.type.indexOf("gif") !== -1) return resolve(file);
        // new Compressor(file, {
        //   // 压缩质量，0-1之间。数字越小，压缩比越高
        //   quality: 0.9,
        //   mimeType: 'image/jpeg',
        //   success(result) {
        //     // 默认返回result是blob类型的文件，可以转成file并返回，交给afterRead钩子进行上传
        //     let newFile = new File([result], file.name, { type: file.type })
        //     resolve(newFile)
        //   },
        //   error(err) {
        //     reject(err)
        //   },
        // })
      });
    });
  });
};
const customUpload: UploadProps['customRequest'] = (e: any) => {
  const formData = new FormData();
  formData.append('multipartFile', e.file);
  qiNiuYunToken({
    bizType: props.bizType,
    resourceType: props.resourceType,
  }).then((res) => {
    if (res.code === 0) {
      const { token, path, agreement, domain } = res.data;
      tokenData.value = {
        token,
        path,
        agreement,
        domain,
      };
      e.onSuccess(e);
    }
  });
};

const handleChange = (info: UploadChangeParam) => {
  // 检查文件大小
  // const maxSizeInMB = props.fileSize;
  // if (info.file.size > maxSizeInMB * 1024 * 1024) {
  //   MessagePlugin.error(`文件大小不能超过 ${maxSizeInMB}MB`);
  //   return;
  // }

  if (info.file.status === 'done') {
    getBase64(info.file.originFileObj, () => {
      loading.value = false;
    });
    const imageReader = new FileReader();
    imageReader.readAsDataURL(info.file.originFileObj);
    imageReader.addEventListener('loadend', async (e) => {
      const imageSrc = e.target.result;
      const res = await calculateImageSize(imageSrc);
      const formData = new FormData();
      const { token, path } = tokenData.value;
      const key = `${path}${info.file.name.split('.')[0]}_${new Date().getTime()}.${info.file.name.split('.')[1]}`;
      formData.append('key', key);
      formData.append('token', token);
      formData.append('file', info.file.originFileObj);
      formData.append('fname', info.file.name);
      const { agreement, domain } = tokenData.value;
      axios
        .post('https://upload.qiniup.com/', formData, {
          headers: {
            'content-type': 'multipart/form-data',
          },
        })
        .then((response) => {
          if (response.data.key) {
            fileList.value = [
              ...fileList.value,
              {
                url: `${agreement}://${domain}/${response.data.key}?wdISI=${JSON.stringify(res)}`,
                name: info.file.name,
                uid: info.file.uid,
              },
            ];
            emits('getUrlList', fileList.value);
          }
        })
        .catch((error) => {
          message.error(`上传失败`);
        });
    });
  }
  if (info.file.status === 'error') {
    loading.value = false;
    message.error('upload error');
  }
};

// 根据图片地址获取图片的宽和高
const calculateImageSize = (url) => {
  return new Promise((resolve, reject) => {
    const image = document.createElement('img');
    image.addEventListener('load', (e: any) => {
      resolve({
        imageWidth: e.target.width,
        imageHeight: e.target.height,
      });
    });
    image.addEventListener('error', () => {
      reject();
    });

    // 将图片的url地址添加到图片地址中
    image.src = url;
  });
};

const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');
const handlePreview = async (file: UploadProps['fileList'][number]) => {
  if (!file.url && !file.preview) {
    file.preview = (await getBase642(file.originFileObj)) as string;
  }
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
};
const handleCancel = () => {
  previewVisible.value = false;
  previewTitle.value = '';
};
const handleDelete = (file: UploadProps['fileList'][number]) => {
  const index = fileList.value.indexOf(file.url);
  fileList.value.splice(index, 1);
  emits('getUrlList', fileList.value);
};
watch(
  () => props.fileList,
  (newValue) => {
    fileList.value = newValue;
    defaultFileList.value = [];
    fileList.value.forEach((element) => {
      defaultFileList.value.push(element);
    });
  },
  { immediate: true, deep: true },
);
</script>
<style scoped>
.avatar-uploader > .ant-upload {
  width: 138px;
  height: 138px;
  overflow: hidden;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
