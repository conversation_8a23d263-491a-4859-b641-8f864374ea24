<template>
  <div class="nav-main">
    <ul class="nav-ul">
      <li
        v-for="item in tabArr"
        :key="item.menuId"
        :class="active === item.nameEn || selectAct === item.nameEn ? 'active-li' : ''"
        @mouseover="handleMouseOver(item.nameEn)"
        @mouseleave="handleMouseLeave()"
        @click="tabClick(item.nameEn)"
      >
        {{ item.name }}
        <span v-if="selectAct == item.nameEn" class="line"></span>
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
// import { getCurrentMenus } from '@/api/login';
// import { useNavStore } from '@/store/modules/navStore'; // 引入store

const route = useRoute();
const router = useRouter();
const active = ref('index');
const tabArr = ref([]);
const selectAct = ref('index');
const tabClick = (subModuleName) => {
  // 顶部tab切换一级路由
  active.value = subModuleName;
  selectAct.value = subModuleName;
  const path = subModuleName.substring(2).charAt(0).toLowerCase() + subModuleName.substring(2).slice(1);
  // const menu = localStorage.getItem('menuList');
  router.push({
    path: `/${path}/index`,
  });
};
const handleMouseOver = (nameEn) => {
  active.value = nameEn; // 鼠标滑过时改变状态
};
const handleMouseLeave = () => {
  active.value = selectAct.value; // 鼠标离开时恢复状态
};

onMounted(() => {
  const menu = JSON.parse(localStorage.getItem('menuList'));
  if (menu.length) {
    // 预定义的顺序
    const tabSort = ['npSettled', 'npMarket', 'npTrade', 'npUser', 'npSystem', 'npSystemManagement', 'npOldPlatform'];
    const tabMap = tabSort.reduce((map, nameEn, index) => {
      map[nameEn] = index;
      return map;
    }, {});

    menu.sort((a, b) => {
      return tabMap[a.nameEn] - tabMap[b.nameEn];
    });
    tabArr.value = menu;
  }
  if (route.path) {
    const path: string = route.path.split('/')[1];
    const str: string = path.charAt(0).toUpperCase() + path.slice(1);
    active.value = `np${str}`;
    selectAct.value = `np${str}`;
  }
});
</script>
<style lang="less" scoped>
.nav-main {
  .nav-ul {
    display: flex;
    flex-direction: row;
    margin-left: 48px;
    width: 100%;
    overflow-x: auto;
    li {
      line-height: 32px;
      font-size: 16px;
      text-align: center;
      color: #a8d0ff;
      font-weight: 500;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      padding: 0 24px;
      .line {
        width: 14px;
        color: #ffffff;
        border: 2px solid #eee;
        border-radius: 5px;
        display: inline-block;
      }
    }
    .active-li {
      color: #ffffff;
    }
  }
}
</style>
