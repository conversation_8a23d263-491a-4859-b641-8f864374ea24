<template>
  <div class="breadcrumb-box">
    <a-breadcrumb>
      <a-breadcrumb-item
        v-for="item in props.crumbs"
        :key="item.id"
        :disabled="item.disabled"
        @click="goToUrl(item.path)"
        >{{ item.title }}</a-breadcrumb-item
      >
    </a-breadcrumb>
  </div>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { usePermissionStore, useSettingStore } from '@/store';
import router from '@/router';

const permissionStore = usePermissionStore();
const { routers: menuRouters } = storeToRefs(permissionStore);
// 定义面包屑项的类型
interface BreadcrumbItem {
  id: string | number;
  title: string;
  path: string; // 确保 path 是字符串类型
  disabled?: boolean;
}

const props = defineProps({
  crumbs: {
    type: Array as () => BreadcrumbItem[], // 使用 BreadcrumbItem 类型
    default: () => [],
  },
});
const emits = defineEmits(['router-path']);
const route = useRoute();
const goToUrl = (path) => {
  if (path) {
    router.push({
      path,
    });
  }
};
</script>
<style scoped>
.breadcrumb-box {
  background-color: #fff;
  padding: 10px 20px;
  cursor: pointer;
}
</style>
