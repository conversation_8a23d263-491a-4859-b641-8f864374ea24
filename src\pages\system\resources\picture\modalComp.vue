<template>
  <div :class="modalType === 'DELETE' ? 'dialog-special dialog-container' : 'dialog-container'">
    <t-dialog v-model:visible="modalInfo.showModal" :header="Title[modalType]" @close="closeFunc" @confirm="onConfirm">
      <div class="content-container">
        <div v-if="modalType === 'DELETE'" class="delete-desc">删除图片不会对已使用图片的业务造成影响。</div>
        <div v-else>
          <t-form ref="form" :data="formData" label-align="top" :rules="rules">
            <template v-for="(item, index) in formList" :key="index">
              <t-form-item
                v-if="(item.belong === modalType || (item.belong === 'COMMON' && modalType !== 'ADD')) && !item.hide"
                :label="item.label"
                :name="item.name"
              >
                <t-select
                  v-if="item.type === 'select'"
                  v-model="formData[item.name]"
                  :options="item.options"
                  clearable
                ></t-select>
                <t-tree-select
                  v-if="item.type === 'treeSelect'"
                  v-model="formData[item.name]"
                  clearable
                  :data="item.options"
                  :tree-props="treeProps"
                />
                <a-button
                  v-if="item.type === 'treeSelect' && modalType === 'UPLOAD'"
                  type="primary"
                  style="margin-left: 10px"
                  @click="addFunc"
                >
                  添加分组
                </a-button>
                <t-input
                  v-if="item.type === 'input'"
                  v-model="formData[item.name]"
                  :maxlength="item.maxLength || 20"
                  :show-limit-number="item.showLimit"
                  placeholder="请输入内容"
                />
                <t-radio-group
                  v-if="item.type === 'radio'"
                  v-model="formData[item.name]"
                  :options="item.options"
                  @change="radioChange($event, item.relateItem)"
                />
                <div v-if="item.type === 'upload'" class="upload-flex">
                  <wd-upload
                    biz-type="in_coming"
                    :file-size="1"
                    :max-width="750"
                    :max-count="10"
                    multiple
                    @get-url-list="afferentUrlChange"
                  />
                  <p>支持.jpg,.jpeg,.png,.gif格式，上传图片宽度限制为750px，大小限制1M，最多一次上传10张</p>
                </div>
              </t-form-item>
            </template>
          </t-form>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import WdUpload from '@/components/WdUpload/index.vue';
import { Title } from './type';
import { formInfo, rulesInfo, formType } from './const';
import { modifyGroup, saveMaterial, saveGroup } from '@/api/resources';

const treeProps = {
  keys: { label: 'name', value: 'id', children: 'children' },
};

const props = defineProps({
  modalInfo: {
    type: Object,
    required: true,
  },
  groupOption: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(['confirm', 'cancel', 'queryGroupFunc']);

const form = ref(null);
const localShowModal = ref(props.modalInfo.showModal);
const modalType = ref();
const fromModal = ref();
const formList = ref<any>(formInfo);
let rules = {
  ...rulesInfo[props.modalInfo.type],
  ...rulesInfo.COMMON,
};
const formData = ref({
  ...formType.COMMON,
  ...formType[props.modalInfo.type],
});
const picInfoList = ref([]);

watch(
  () => props.modalInfo,
  (value) => {
    localShowModal.value = value.showModal;
    modalType.value = value.type;
    if (value.type) {
      rules = {
        ...rulesInfo[value.type],
        ...rulesInfo.COMMON,
      };
    }
    if (value.type === 'ADD') {
      formData.value = {};
      formData.value.level = 1;
      const obj = formInfo.find((item) => item.name === 'level');
      radioChange(1, obj.relateItem);
    } else {
      formList.value = formInfo;
    }
    if (props.modalInfo.formInfo && Object.keys(props.modalInfo.formInfo).length) {
      const { name, ownerGroupId } = props.modalInfo.formInfo;
      formData.value.name = name;
      if (ownerGroupId && ownerGroupId !== '0') {
        formData.value.ownerGroupId = ownerGroupId;
      }
      if (value.type === 'EDIT') {
        const { id, name, url } = props.modalInfo.formInfo;
        picInfoList.value = [
          {
            id,
            name,
            url,
          },
        ];
      }
    }
  },
  { deep: true },
);

watch(
  () => props.groupOption,
  (value) => {
    if (value.length) {
      formInfo.forEach((item) => {
        if (item.name === 'parentId' || item.name === 'ownerGroupId') {
          if (item.belong === 'ADD') {
            const arr = [];
            value.forEach((items: any) => {
              arr.push({
                label: items.name,
                value: items.id,
              });
            });
            item.options = arr;
          } else {
            item.options = [
              {
                id: '0',
                level: 1,
                name: '未分组',
                parentId: '0',
                children: null,
              },
              ...value,
            ];
            formData.value.ownerGroupId = '0';
          }
        }
      });
      formList.value = formInfo;
    }
  },
);

// 分类切换
const radioChange = (e, list) => {
  if (list) {
    const arr = JSON.parse(JSON.stringify(formInfo));
    arr.forEach((item) => {
      const curr: number = list.findIndex((items) => items === item.name);
      if (curr !== -1) {
        item.hide = e === 1;
      }
    });
    formList.value = arr;
  }
};

const onConfirm = () => {
  if (formData.value.level === 1) {
    formData.value.parentId = null;
  }
  if (form.value) {
    form.value.validate().then((res) => {
      if (res === true) {
        modalType.value = props.modalInfo.type;
        if (fromModal.value) {
          rules = {
            ...rulesInfo[props.modalInfo.type],
            ...rulesInfo.COMMON,
          };
          saveGroup({
            ...formData.value,
          }).then((res) => {
            if (res.code === 0) {
              MessagePlugin.success('新增成功！');
              emits('queryGroupFunc');
              form.value.reset();
            }
          });
          fromModal.value = false;
        } else {
          const obj = {
            UPLOAD: {
              msg: '上传',
              callback: () => {
                const obj: any = {
                  fileResourceDetailDTOS: picInfoList.value,
                };
                if (formData.value.ownerGroupId && formData.value.ownerGroupId !== '0') {
                  obj.ownerGroupId = formData.value.ownerGroupId;
                }
                return saveMaterial(obj);
              },
            },
            DELETE: '',
            MODIFY: {
              msg: '修改',
              callback: () =>
                modifyGroup({
                  materialIdList: props.modalInfo.checkList,
                  ...formData.value,
                }),
            },
            ADD: {
              msg: '新增',
              callback: () => saveGroup({ ...formData.value }),
            },
            EDIT: {
              msg: '修改',
              callback: () => {
                const obj = picInfoList.value[0];
                const list = [
                  {
                    ...obj,
                    name: formData.value.name,
                  },
                ];
                const info: any = {
                  fileResourceDetailDTOS: list,
                };
                if (formData.value.ownerGroupId !== '0') {
                  info.ownerGroupId = formData.value.ownerGroupId;
                } else if (props.modalInfo.formInfo.ownerGroupId !== '0') {
                  MessagePlugin.error('素材分组未发现');
                  return Promise.reject();
                }
                return saveMaterial(info);
              },
            },
          };
          obj[props.modalInfo.type].callback().then((res) => {
            if (res.code === 0) {
              MessagePlugin.success(`${obj[props.modalInfo.type].msg}成功！`);
              form.value.reset();
              formData.value = {
                ...formType.COMMON,
                ...formType[props.modalInfo.type],
              };
              emits('confirm');
            }
          });
        }
      }
    });
  }
};

const closeFunc = () => {
  if (fromModal.value) {
    modalType.value = props.modalInfo.type;
    rules = {
      ...rulesInfo[props.modalInfo.type],
      ...rulesInfo.COMMON,
    };
    fromModal.value = false;
  } else {
    form.value.reset();
    picInfoList.value = [];
    formData.value = {
      ...formType.COMMON,
      ...formType[props.modalInfo.type],
    };
    emits('cancel');
  }
  localShowModal.value = false;
};

// 添加分组
const addFunc = () => {
  rules = {
    ...rulesInfo.ADD,
    ...rulesInfo.COMMON,
  };
  form.value.reset();
  fromModal.value = true;
  modalType.value = 'ADD';
  formData.value.level = 1;
  const obj = formInfo.find((item) => item.name === 'level');
  radioChange(1, obj.relateItem);
};

// 图片上传
const afferentUrlChange = (list) => {
  formData.value.fileList = list;
  picInfoList.value = list;
};
</script>

<style lang="less" scoped>
.dialog-container {
  :deep(.t-dialog__header) {
    height: 73px;
    padding: 10px 32px 0;
  }

  :deep(.t-dialog__body__icon) {
    padding-top: 0;
  }

  .content-container {
    padding: 0 30px;

    .delete-desc {
      font-weight: 400;
      font-size: 14px;
      color: #495366;
    }
  }
  // .add-btn {
  //   width: 75px;
  //   font-size: 14px;
  //   color: #1a7af8;
  //   margin-left: 20px!important;
  //   cursor: pointer;
  // }
}

.dialog-special {
  :deep(.t-dialog__header) {
    border: none;
  }
}
</style>
