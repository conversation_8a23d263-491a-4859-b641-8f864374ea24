<template>
  <div>
    <div @click="handleClick">
      <slot>
        <a-link type="primary" hover="color">{{ isDefault ? '确定' : text }}</a-link>
      </slot>
    </div>
    <!-- 二次确认弹框 -->
    <a-modal v-model:visible="visible" :title="`你确定要${text}吗？`" @close="handleClose" @confirm="handleConfirm">
      <div v-if="desc">
        <div style="margin-left: 30px; color: #495366">{{ desc }}</div>
      </div>
    </a-modal>
  </div>
</template>
<script></script>
<script setup>
import { ref } from 'vue';

const props = defineProps({
  isDefault: {
    type: Boolean,
    default: false,
  },
  text: {
    type: String,
    default: '',
  },
  desc: {
    type: String,
    default: '',
  },
  params: {
    type: [String, Number, Boolean, Array, Object, Function],
    default: () => ({}),
  },
  onConfirm: {
    type: Function,
  },
  onValidate: {
    type: Function,
  },
});

const visible = ref(false);

// 点击事件
const handleClick = (e) => {
  e.stopPropagation();
  if (props.onValidate) {
    props.onValidate(() => {
      visible.value = true;
    });
  } else {
    visible.value = true;
  }
};

// 关闭弹框事件
const handleClose = ({ e }) => {
  e.stopPropagation();
};

// 确认事件
const handleConfirm = ({ e }) => {
  e.stopPropagation();
  visible.value = false;
  props.onConfirm(props.params);
};
</script>
<style lang="less" scoped>
:deep(.t-dialog__header) {
  height: 70px;
  padding: 10px 32px 0;
  border: none;
}
:deep(.t-dialog__body__icon) {
  padding-top: 0;
}
</style>
