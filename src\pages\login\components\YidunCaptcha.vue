<template>
  <div>
    <!-- 容器元素用于嵌入模式 -->
    <div id="captcha-container" class="code-box"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';

const props = defineProps({
  refreshCode: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(['get-code']);
// 存储验证码实例
const captchaInstance = ref(null);

// 初始化易盾验证码
const initCaptcha = () => {
  // 确保容器元素已渲染
  nextTick(() => {
    window.initNECaptcha(
      {
        captchaId: '2bb9bab4bd6743bd9b3e7c99a538f9b2', // 本项目的captchaId
        element: '#captcha-container', // 绑定到 #captcha-container 容器元素
        mode: 'embed', // 使用嵌入模式
        onVerify: (err, data) => {
          if (err) {
            console.error('验证失败:', err);
          } else {
            console.log('验证成功:', data);
            // 后续可以将 data 发送给后端验证
            emit('get-code', data.validate);
          }
        },
      },
      (instance) => {
        console.log('Captcha 实例:', instance);
        captchaInstance.value = instance; // 保存实例
      },
      (err) => {
        console.error('验证码初始化失败:', err);
      },
    );
  });
};

// 动态加载易盾 SDK 脚本
const injectScript = (src, callback) => {
  if (document.querySelector(`script[src="${src}"]`)) {
    callback(); // 如果脚本已存在，直接调用回调
    return;
  }
  const script = document.createElement('script');
  script.src = src;
  script.onload = callback;
  script.onerror = () => {
    console.error('验证码脚本加载失败');
  };
  document.body.appendChild(script);
};
watch(
  () => props.refreshCode,
  (newData) => {
    console.log('重新涮');
    if (newData) {
      injectScript('https://cstaticdun.126.net/load.min.js', initCaptcha);
    }
  },
  { immediate: true, deep: true },
);
// 在组件挂载后加载脚本并初始化验证码
onMounted(() => {
  injectScript('https://cstaticdun.126.net/load.min.js', initCaptcha);
});
</script>

<style scoped>
button {
  padding: 10px 20px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}
</style>
