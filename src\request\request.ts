import jwtDecode from 'jwt-decode';

import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';
import { refreshToken } from '@/api/login';
import { TokenProvider, tokenStore } from './tokenProvider';
import FetchError from './errors/FetchError';
import GeneralError from './errors/GeneralError';
import TokenError from './errors/TokenError';
import UnknownError from './errors/UnknownError';
import router from '@/router';

const count = ref(0);
const isToken = ref(true);

function transLong2String(data: any) {
  if (Object.prototype.toString.call(data) === '[object Object]') {
    for (const key in data) {
      if (/^\d+$/.test(data[key])) {
        data[key] = BigInt(data[key]).toString().length > 15 ? BigInt(data[key]).toString() : data[key];
      } else if (Object.prototype.toString.call(data) === '[object Object]') {
        transLong2String(data[key]);
      }
    }
  } else if (Object.prototype.toString.call(data) === '[object Array]') {
    data.forEach((item) => transLong2String(item));
  }
  return data;
}

export interface RequestParams {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  headers?: { [key: string]: string };
  tokenProvider?: TokenProvider;
  data?: FormData | { [key: string]: any };
  includeCredentials?: boolean;
}

const buildPayload = async (response: Response) => {
  const responseType = response.headers.get('content-type') || '';
  if (responseType.startsWith('text/')) {
    return response.text();
  }
  if (responseType.indexOf('application/json') !== -1) {
    return response.json();
  }
  return response.blob();
};

const codeList = [802901, 802902, 802903, 802471]; // 接口返回code码都需要重新登录

const buildResponse = async <T>(response: Response) => {
  const { status, statusText } = response;
  const payload = await buildPayload(response);
  const path = router.options.history.location;
  if (codeList.includes(Number(payload.code))) {
    router.push({
      path: '/login',
      query: { redirect: encodeURIComponent(path) },
    });
    throw new GeneralError(status, payload.code, payload.message);
  }
  if (payload.code === 0 && typeof payload?.data === 'object') {
    transLong2String(payload.data);
  }
  // 接口正常，但业务接口code错误
  if (status === 200 && payload.code !== 0) {
    if (typeof payload === 'object' && payload.code && payload.message) {
      MessagePlugin.error({
        content: `${payload.code}: ${payload.message}`,
      });
      return payload as T;
    }
  }
  if (status === 401 || payload.code === 401) {
    throw new TokenError(payload.message ?? statusText);
  }
  // if (status === 403 || payload.code === 403) {
  //   throw new TokenError(payload.message ?? statusText);
  // }
  if ((status >= 400 && status < 600) || (payload.code >= 400 && payload.code < 600)) {
    if (typeof payload === 'object' && payload.code && payload.message) {
      throw new GeneralError(status, payload.code, payload.message);
    }
    throw new UnknownError(status, statusText);
  }
  if (status > 600 || payload.code > 600) {
    if (typeof payload === 'object' && payload.code && payload.message) {
      throw new GeneralError(status, payload.code, payload.message);
    }
    throw new UnknownError(status, statusText);
  }
  return payload as T;
};

const { VITE_API_URL } = import.meta.env;
const request = async <T>(params: RequestParams): Promise<T> => {
  count.value = 0;
  const { method, data, includeCredentials, headers } = params;
  let tokenProvider;
  if (localStorage.getItem('user-auth')) {
    const token = JSON.parse(localStorage.getItem('user-auth')).accessToken;
    const getToken = JSON.parse(localStorage.getItem('user-auth'));
    if (token) {
      const decoded: any = jwtDecode(token);
      const currentTime = Date.now() / 1000;
      if (decoded.exp < currentTime) {
        console.log('token已过期', isToken.value);
        if (isToken.value) {
          // 判断是否是刷新接口，防止无限循环调用
          isToken.value = false;
          const res = await refreshToken({
            refreshToken: getToken.refreshToken,
          });
          if (res.code === 0) {
            tokenProvider = res.data;
            localStorage.setItem('user-auth', JSON.stringify(res.data));
            isToken.value = true;
            console.log('token过期调用', res.data);
          }
        }
      } else {
        isToken.value = true;
        tokenProvider = getToken;
      }
    }
  }

  const path = VITE_API_URL + params.path;
  const isRequestJson = method !== 'GET' && !(data instanceof FormData);
  let response: Response;
  console.log('880');
  try {
    response = await window.fetch(path, {
      method,
      headers: {
        ...(isRequestJson ? { 'Content-Type': 'application/json' } : {}),
        ...(tokenProvider ? { Authorization: `${tokenProvider.accessToken}` } : {}),
        ...(headers || {}),
        // 'X-Accept-Version': 'yanghongfa', // 杨宏发
        // 'X-Accept-Version': 'wx1', // 王旭
      },
      ...(method === 'GET' ? {} : { body: data instanceof FormData ? data : JSON.stringify(data) }),
      ...(includeCredentials ? { credentials: 'include' } : {}),
    });
  } catch (e) {
    throw new FetchError(`fetch error ${e}`);
  }
  let requestError: any;
  try {
    return await buildResponse<T>(response);
  } catch (e) {
    if (e.message === 'Unauthorized') {
      const userAuth = JSON.parse(localStorage.getItem('user-auth'));
      const { accessToken, refreshToken } = userAuth;
      tokenStore.setToken(accessToken);
      tokenStore.setRefreshToken(refreshToken);
    }
    requestError = e;
  }
  if (tokenProvider && requestError instanceof TokenError) {
    // accessToken 失效
    if (!tokenProvider.refreshToken && !tokenProvider.refresh) {
      throw tokenProvider.customizedError || requestError;
    }
    let accessToken: string;
    try {
      // 刷新 accessToken
      accessToken = await tokenProvider.refresh?.();

      if (!accessToken) {
        throw tokenProvider.customizedError || requestError;
      } else {
        const userAuth = JSON.parse(localStorage.getItem('user-auth')) || { accessToken: '', refreshToken: '' };
        localStorage.setItem('user-auth', JSON.stringify({ ...userAuth, accessToken }));
      }
    } catch {
      // const path = router.options.history.location;
      // setTimeout(() => {
      //   router.push({
      //     path: '/login',
      //     query: { redirect: encodeURIComponent(path), project: 'market' },
      //   });
      // }, 1000);
      throw requestError;
    }
    return request<T>({
      ...params,
      tokenProvider: {
        accessToken,
        customizedError: tokenProvider.customizedError,
        refresh: tokenProvider.refresh,
      },
    });
  }
  console.log(count, 'count');
  count.value += 1;
  if (count.value === 1) {
    // 错误信息提示
    MessagePlugin.error({
      content: requestError.message || requestError.statusText,
    });
  }

  throw requestError;
};

export default request;
