import dayjs from 'dayjs';

interface Subtract {
  dt: string;
  nums: number;
  type: any;
  format: string;
}

const defaultFormat = 'YYYY-MM-DD HH:mm:ss'; // 默认日期格式，比如：'9999-12-31 11:59:59'

// 校验日期是否有效
export function isValid(dt: string): boolean {
  return dayjs(dt).isValid();
}

// 输入指定日期格式
export function format(dt: string, format = defaultFormat): string | null {
  if (isValid(dt)) {
    return dayjs(dt).format(format);
  }
  return null;
}

// 获取当前日期
export function getNow(format = defaultFormat): string {
  return dayjs().format(format);
}

/**
 * 计算相差日期，比如最近3天、最近7天、最近1个月、最近1年......
 * @param dt 需要用来对比的日期
 * @param nums 相差多少
 * @param type 比较类型，比如 天 => 'day'
 * @param format 输出的格式
 */
export function subtract({ dt = getNow(), nums, type, format = defaultFormat }: Subtract) {
  if (isValid(dt)) {
    return dayjs(dt).subtract(nums, type).format(format);
  }
  return null;
}

// 将2024年11月02日的格式转换成指定的格式
export function formatChineseDate(chineseDate, format = 'YYYYMMDD') {
  const regex = /(\d{4})年(\d{1,2})月(\d{1,2})日/;
  const match = chineseDate.match(regex);

  if (match) {
    const year = match[1];
    const month = match[2].padStart(2, '0'); // 确保月份是两位数
    const day = match[3].padStart(2, '0'); // 确保日期是两位数

    return `${year}${month}${day}`;
  }

  if (chineseDate === '长期') {
    return dayjs('2999-12-30').format(format);
  }

  throw new Error('日期格式不正确');
}
