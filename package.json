{"name": "woody-living-plat-new-base", "version": "0.6.1", "scripts": {"dev": "vite --open --mode dev", "test": "vite --open --mode test", "pre": "vite --open --mode pre", "build": "vue-tsc --noEmit && vite build --mode prod", "build:dev": "vue-tsc --noEmit && vite build --mode dev", "build:test": "vue-tsc --noEmit && vite build --mode test", "build:pre": "vue-tsc --noEmit && vite build --mode pre", "build:prod": "vue-tsc --noEmit && vite build --mode prod", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,vss,sass,less}", "prepare": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || is-ci || husky install", "site:preview": "npm run build && cp -r dist _site", "test:coverage": "echo \"no test:coverage specified,work in process\""}, "dependencies": {"@micro-zoe/micro-app": "^1.0.0-rc.12", "axios": "^1.1.3", "compressorjs": "^1.2.1", "dayjs": "^1.11.7", "file-saver": "^2.0.5", "jszip": "^3.10.1", "jwt-decode": "3.1.2", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.0.11", "pinia-plugin-persistedstate": "^3.0.1", "qiniu-js": "3.1.2", "qs": "^6.12.3", "sass": "^1.77.4", "tdesign-icons-vue-next": "^0.2.4", "tdesign-vue-next": "^1.9.6", "tvision-color": "^1.3.1", "vue": "^3.2.31", "vue-clipboard3": "^2.0.0", "vue-router": "~4.1.5", "vuedraggable": "^4.1.0", "woody-ui": "^1.0.1"}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@types/echarts": "^4.9.10", "@types/lodash": "^4.14.182", "@types/qs": "^6.9.7", "@types/ws": "^8.2.2", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "@vitejs/plugin-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^1.1.7", "@vue/compiler-sfc": "^3.0.5", "@vue/eslint-config-typescript": "^11.0.0", "commitizen": "^4.2.4", "cz-conventional-changelog": "^3.3.0", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.2.0", "eslint-plugin-vue-scoped-css": "^2.2.0", "husky": "^7.0.4", "less": "^4.1.1", "lint-staged": "^12.1.2", "mockjs": "^1.1.0", "prettier": "^2.4.1", "stylelint": "~13.13.1", "stylelint-config-prettier": "~9.0.3", "stylelint-less": "1.0.1", "stylelint-order": "~4.1.0", "typescript": "~4.8.4", "vite": "^2.7.1", "vite-plugin-mock": "^2.9.6", "vite-svg-loader": "^3.1.0", "vue-tsc": "^1.0.8", "vue-turnstile": "^1.0.9"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "npm run lint:fix", "git add ."], "*.{html,vue,vss,sass,less}": ["npm run stylelint:fix", "git add ."]}, "description": "我店生活新后管"}