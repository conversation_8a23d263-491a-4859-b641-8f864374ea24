{"compilerOptions": {"target": "esnext", "allowJs": true, "module": "esnext", "moduleResolution": "node", "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "lib": ["esnext", "dom"], "types": ["vite/client"], "noEmit": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["**/*.ts", "src/**/*.d.ts", "src/types/**/*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "node_modules/tdesign-vue-next/global.d.ts", "global.d.ts", "src/store/navStore.js"]}