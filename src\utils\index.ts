import { PIC_PREFIX, PIC_TAG } from '@/config/global';
import { globalStore } from '@/store';

/**
 * 判断是否为字符串类型
 * @param val 判断的值
 * @returns Boolean 类型
 */
export const isString = (val: string): Boolean => typeof val === 'string';

/**
 * 判断是否为对象
 * @param obj 判断的数据
 * @returns Boolean 类型
 */
export const isObj = (obj: Object): Boolean => Object.prototype.toString.call(obj) === '[object Object]';

/**
 * 判断是否为空值
 * @param val 判断的值
 * @returns Boolean 类型
 */
export const isEmptyValue = (val: any): Boolean => {
  if (val === null || val === undefined || (isString(val) && !val.trim().length)) {
    return true;
  }
  return false;
};

/**
 * 设置所属分类全局列表数据，并过滤掉不显示的项，即 show !== 1
 * @param isUpdate 是否需要更新：true =》需要；false =》不需要
 * @returns Promise
 */
export const getCategoryDropDown = async (isUpdate = false): Promise<any> => {
  const $globalStore = globalStore();
  if (!isUpdate && 0 in $globalStore.subcategoryOptions) {
    return $globalStore.subcategoryOptions;
  }
  // const res = await queryShopCategoryDropDown();
  // if (res.code === 0 && Array.isArray(res.data)) {
  //   const filterArr = filterCategoryDropDown(res.data);
  //   $globalStore.getSubcategoryOptions(filterArr);
  //   $globalStore.setExpendOptions(expendData(filterArr, ['id'], 'childrenShopIndustryCategory'));
  //   return filterArr;
  // }
  return Promise.resolve([]);
};

/**
 * 展开所有数据，以Map形式展示。（柯里化，方便后续根据 key 值拿到对应的数据）
 * @param data 处理的数据，数组类型
 * @param keys 用于扩展后的key名，后续根据这个key取指定数据。数组形式
 * @returns Map 类型
 */
export const expendData = (data: Array<any>, keys: Array<string>, childrenName = 'areas'): Object => {
  const map = new Map();
  const fn = (data) => {
    if (Array.isArray(data)) {
      data.forEach((item) => {
        const children = item[childrenName];
        delete item[childrenName];
        if (Array.isArray(keys)) {
          keys.forEach((key) => {
            map.set(item[key], item);
          });
        }
        if (Array.isArray(children)) {
          fn(children);
        }
      });
    }
  };
  fn(JSON.parse(JSON.stringify(data)));
  return map;
};

/**
 * 将平铺数据转换成 tree
 * @param data 需要处理的数据，数组类型
 * @returns 数组类型
 */
export const setTreeData = (data: Array<object>): Array<object> => {
  if (Array.isArray(data)) {
    const result: Array<object> = [];
    const resultMap: Map<number, any> = new Map();
    let level = 1;
    const fn = (data, map) => {
      const operationMap = new Map();
      const childrenData: Array<object> = [];
      data.forEach((item) => {
        if (item.level === level) {
          const parent = map.get(item.parentId);
          operationMap.set(item.id, item);
          if (isObj(parent)) {
            if (!Array.isArray(parent.children)) {
              parent.children = [];
            }
            item.parentName = parent.categoryName;
            parent.children.push(item);
          } else {
            resultMap.set(item.id, item);
          }
        } else {
          childrenData.push(item);
        }
      });
      if (0 in childrenData) {
        level++;
        fn(childrenData, operationMap);
      }
    };
    fn(data, resultMap);

    resultMap.forEach((item) => {
      result.push(item);
    });

    return result;
  }
  return data;
};

export const uploadPicPath = (val: string): string => {
  if (!isString(val) || isEmptyValue(val)) {
    return '';
  }
  let arr = val.split(',');
  arr = arr.map((url, index) => {
    if (index === 0) {
      url = url.replace(PIC_PREFIX, `${PIC_PREFIX}${PIC_TAG}`);
    } else {
      url = url.replace(PIC_PREFIX, '');
    }
    return url;
  });
  return arr.join(',');
};

/**
 * 匹配图片格式
 * @param val 处理的数据，字符串类型
 * @returns 字符串类型
 */
export const setPicPath = (val: string): string => {
  if (!isString(val) || isEmptyValue(val)) {
    return '';
  }
  const arr = val.split(',');
  const mapArr = arr.map((item) => {
    if (item.indexOf(`${PIC_PREFIX}${PIC_TAG}`) === -1) {
      if (item.indexOf(PIC_PREFIX) === -1) {
        return `${PIC_PREFIX}${item}`;
      }
      return item;
    }
    return item.replace(`${PIC_PREFIX}${PIC_TAG}`, PIC_PREFIX);
  });
  return mapArr.join(',');
};

/**
 * 过滤掉所属分类列表 show !== 1 的数据
 * @param data 处理的数据，数组类型
 * @returns 数组类型
 */
export const filterCategoryDropDown = (data: Array<any>): any => {
  if (Array.isArray(data)) {
    const filterArr = [];
    const fn = (data, arr) => {
      data.forEach((item) => {
        if (item.show === 1) {
          let obj = {} as any;
          const { childrenShopIndustryCategory, ...rest } = item;
          obj = rest;
          if (Array.isArray(childrenShopIndustryCategory) && 0 in childrenShopIndustryCategory) {
            obj.childrenShopIndustryCategory = [];
            fn(childrenShopIndustryCategory, obj.childrenShopIndustryCategory);
          }
          arr.push(obj);
        }
      });
    };
    fn(data, filterArr);
    return filterArr;
  }
  return [];
};
