<template>
  <l-breadcrumb style="background: none; margin-top: 10px" :crumbs="crumbs" />
  <div style="padding-right: 15px">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-css">
      <p v-if="isShow" class="table-p">
        <span>团队名称:</span>
        <span class="ml10">{{ infoObj.teamName }}</span>
        <span class="ml10">账号名称:</span>
        <span class="ml10">{{ infoObj.createUserName }}</span>
        <span class="ml10">角色名称:</span>
        <span class="ml10">{{ infoObj.roleNames }}</span>
      </p>

      <a-button type="primary" class="add-css mt10" @click="addClick">新增</a-button>
      <!-- 注意组件父元素的宽度 -->
      <div style="width: 100%" class="tdesign-demo-block-column-large tdesign-demo__table-affix">
        <a-table
          :key="tableKey"
          row-key="index"
          :data-source="countData"
          :columns="columns"
          :scroll="{ x: 1000 }"
          :pagination="pagination"
          @change="onPageChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 1 ? 'blue' : 'red'">
                {{ statusNameListMap[record.status].label }}
              </a-tag>
            </template>
            <template v-if="column.key === 'roleName'">
              <a-popover>
                <template #content>
                  <p class="p_css">{{ record.roleName }}</p>
                </template>
                <p class="text_css">{{ record.roleName }}</p>
              </a-popover>
            </template>
            <template v-if="column.key === 'operation'">
              <a-button type="link" class="btn-css" @click="lookdetail(record)">查看</a-button>
              <a-button type="link" class="btn-css" @click="editdetail(record)">编辑</a-button>
              <a-dropdown>
                <a-button type="link" class="btn-css" @click.prevent>
                  更多
                  <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item>
                      <span class="btn-css mr10" @click="shouClick(record)">授权</span>
                    </a-menu-item>
                    <a-menu-item>
                      <a-popconfirm
                        ok-text="确定"
                        cancel-text="取消"
                        title="确认重置吗？"
                        @confirm="resetClick(record)"
                      >
                        <span class="btn-css mr10">重置</span>
                      </a-popconfirm>
                    </a-menu-item>
                    <a-menu-item>
                      <a-popconfirm
                        ok-text="确定"
                        cancel-text="取消"
                        :title="record.status === 1 ? '确认禁用吗？' : '确认启用吗？'"
                        @confirm="disOrUpClick(record)"
                      >
                        <span class="btn-css">{{ record.status === 1 ? '禁用' : '启用' }}</span>
                      </a-popconfirm>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
  <a-modal
    :title="titleType"
    width="30%"
    :close-on-overlay-click="true"
    :visible="addVisible"
    :on-close="onClose"
    :destroy-on-close="true"
    @cancel="addVisible = false"
    @ok="clickSubmit"
  >
    <div class="dialog-pad mt20">
      <a-form ref="formRef" :rules="FORM_RULES" :model="formData" direction="vertical">
        <a-form-item label="账号名称" name="userName">
          <a-input
            v-model:value="formData.userName"
            placeholder="请输入内容"
            :maxlength="30"
            show-limit-number
            :disabled="isDis"
          />
        </a-form-item>
        <a-form-item label="联系电话" name="mobile">
          <a-input
            v-model:value="formData.mobile"
            placeholder="请输入"
            type="text"
            :disabled="isDis"
            :maxlength="11"
            show-limit-number
          />
        </a-form-item>
      </a-form>
    </div>
    <template v-if="isDis" #footer>
      <span></span>
    </template>
  </a-modal>
  <a-modal
    title="提醒"
    width="30%"
    :close-on-overlay-click="true"
    :visible="userVisible"
    :on-close="onUserClose"
    @cancel="userVisible = false"
  >
    <div class="dialog-pad mt20">
      <a-form ref="formRef" direction="vertical">
        <a-form-item :label="isReset ? '创建成功' : '重置成功'"> </a-form-item>
        <a-form-item label="账号" name="userName">
          <span>{{ userObj.userName }}</span>
        </a-form-item>
        <a-form-item label="初始密码" name="mobile">
          <span>{{ userObj.passWord }}</span>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button v-if="isReset" type="link" @click="shouClick(userObj)">请授权</a-button>
      <a-button type="link" @click="copyClick">复制账号密码</a-button>
    </template>
  </a-modal>
  <a-modal
    title="授权"
    width="40%"
    :close-on-overlay-click="true"
    :visible="authVisible"
    :on-close="onAuthClose"
    :destroy-on-close="true"
    @cancel="authVisible = false"
    @ok="authSubmit"
  >
    <div class="dialog-pad mt20">
      <a-form ref="formRef" direction="vertical">
        <a-form-item label="账号" name="userName">
          <span>{{ userObj.userName }}</span>
        </a-form-item>
        <a-form-item label="角色" name="mobile">
          <a-checkbox-group v-model:value="roleIds">
            <a-checkbox v-for="item in roleData" :key="item.roleId" :value="item.roleId">{{
              item.roleName
            }}</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
<script setup lang="tsx">
import { ref, watch, computed, reactive, onMounted } from 'vue';
import { MessagePlugin, FormInstanceFunctions, FormProps } from 'tdesign-vue-next';
import useClipboard from 'vue-clipboard3';
import { CheckCircleFilledIcon, CloseCircleFilledIcon } from 'tdesign-icons-vue-next';
import { useRoute } from 'vue-router';
import LBreadcrumb from '@/components/Breadcrumb.vue';
import SearchAntd from '@/components/SearchAntd/index.vue';
import {
  getPlatUserList,
  banPlatUser,
  addPlatUser,
  resetPassword,
  grantUser,
  getPlatRoleApi,
  getTeamPlatUserList,
  addTeamPlatUser,
  grantTeamUser,
  getTeamPlatRoleApi,
  banTeamRolePlatUser,
  updatePlatUser,
  updateTeamPlatUser,
  getUserRoleRel,
  getUserTeamRoleRel,
  getPlatTeamById,
} from '@/api/system';
import { validateContactNumber } from '@/utils/common';
import { DownOutlined } from '@ant-design/icons-vue';

const route = useRoute();
const { toClipboard } = useClipboard();
const addVisible = ref(false);
const userVisible = ref(false);
const authVisible = ref(false);
const isReset = ref(true);
const isShow = ref(false);
const crumbs = ref<any>([]);
const getBreadcrumb = () => {
  crumbs.value = [
    {
      title: '系统',
      path: '',
      disabled: true,
      id: 1,
    },
    {
      title: route.query.teamAdminId ? '团队管理' : '管理员管理',
      path: route.query.teamAdminId ? '' : '/teamManangment/teamList',
      disabled: false,
      id: 2,
    },
    {
      title: '团队列表',
      path: '/teamManangment/teamList',
      disabled: false,
      id: 3,
    },
    {
      title: route.query.teamAdminId ? '成员管理' : '账号管理',
      path: route.query.teamAdminId ? '' : '/adminManangment/countManangment',
      disabled: false,
      id: 3,
    },
  ];
  if (!route.query.teamAdminId) crumbs.value.splice(2, 1); // 从团队管理切到管理员管理，需要删掉团队管理面包屑
};
const infoObj = reactive({
  teamName: '',
  createUserName: '',
  roleNames: '',
});
const roleData = ref([]);
const formList = ref([
  {
    label: '账号名称',
    name: 'userName',
    type: 'input', // 输入框
    span: 6,
  },
  {
    label: '角色',
    name: 'roleIds',
    type: 'select', // 输入框
    placeholder: '请选择',
    options: roleData,
    valueKey: 'roleId',
    labelKey: 'roleName',
    mode: 'multiple',
    showSearch: true,
    needFilter: true,
    span: 6,
  },
  {
    label: '状态',
    name: 'status',
    type: 'select', // 输入框
    span: 6,
    options: [
      {
        label: '禁用',
        value: 0,
      },
      {
        label: '启用',
        value: 1,
      },
    ],
  },
]);
const isDis = ref(false);
const isEdit = ref(false);
const titleType = computed(() => (isEdit.value ? '详情' : '新增'));

const userObj = reactive({
  userName: '',
  userId: '',
  passWord: '',
});
const serachObj = reactive({
  status: null,
  userName: '',
  roleIds: [],
});
const roleIds = ref([]);
const formRef = ref<FormInstanceFunctions>(null);
const FORM_RULES: FormProps['rules'] = {
  userName: [
    {
      required: true,
      message: '账号名称必填',
    },
  ],
  mobile: [
    {
      required: true,
      message: '联系电话必填',
    },
  ],
};
const formData: FormProps['data'] = reactive({
  userName: '',
  userId: '',
});
const statusNameListMap = {
  1: { label: '启用', theme: 'success', icon: <CheckCircleFilledIcon /> },
  0: { label: '禁用', theme: 'danger', icon: <CloseCircleFilledIcon /> },
};

const columns = [
  {
    title: '账号ID',
    dataIndex: 'userId',
    key: 'userId',
    fixed: 'left',
    align: 'left',
    width: 200,
  },
  {
    title: '账号名称',
    dataIndex: 'userName',
    key: 'userName',
    align: 'left',
    width: 200,
  },
  {
    title: '联系电话',
    dataIndex: 'mobile',
    key: 'mobile',
    align: 'left',
    width: 200,
  },
  {
    title: '角色',
    dataIndex: 'roleName',
    key: 'roleName',
    align: 'left',
    width: 300,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    align: 'left',
    width: 150,
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
    align: 'left',
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    align: 'left',
    width: 200,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    align: 'left',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'left',
    fixed: 'right',
    width: 260,
  },
];

const handleSearch = (param) => {
  const { status, userName, roleIds } = param;
  pagination.value.defaultCurrent = 1;
  pagination.value.defaultPageSize = 10;
  tableKey.value = Math.random();
  serachObj.status = status;
  serachObj.userName = userName;
  serachObj.roleIds = roleIds;
  getList();
};

const countData = ref([]);

const tableKey = ref(0);

const pagination = ref({ defaultCurrent: 1, defaultPageSize: 10, total: 0 });

// 分页
const onPageChange = (e) => {
  pagination.value.defaultCurrent = e.current;
  pagination.value.defaultPageSize = e.pageSize;
  getList();
};
// 列表查询
const getList = async () => {
  const { teamAdminId, teamId } = route.query;
  const { status, userName, roleIds } = serachObj;
  const params = {
    roleIds,
    page: pagination.value.defaultCurrent,
    size: pagination.value.defaultPageSize,
    status,
    userName,
    teamAdminId,
    teamId,
  };
  const res = teamAdminId ? await getTeamPlatUserList(params) : await getPlatUserList(params);
  if (res.code === 0) {
    const { total, records } = res.data;
    pagination.value.total = total;
    countData.value = records;
  }
};
const onUserClose = () => {
  userVisible.value = false;
};
const onAuthClose = () => {
  authVisible.value = false;
  setTimeout(() => {
    formData.userName = '';
    formData.mobile = '';
    roleIds.value = [];
  });
};

// 查看详情
const lookdetail = (row) => {
  isDis.value = true;
  isEdit.value = true;
  formData.userName = row.userName;
  formData.mobile = row.mobile;
  addVisible.value = true;
};

// 新增
const addClick = () => {
  addVisible.value = true;
  isEdit.value = false;
  isDis.value = false;
};

// 编辑
const editdetail = (row) => {
  isEdit.value = true;
  isDis.value = false;
  formData.userName = row.userName;
  formData.mobile = row.mobile;
  formData.userId = row.userId;
  addVisible.value = true;
};
// 添加确定
const clickSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      const { teamAdminId, teamId } = route.query;
      const { userName, mobile, userId } = formData;
      const params = {
        userName,
        mobile,
        teamAdminId,
        userId,
        teamId,
      };
      const inputRegExp = /^[a-zA-Z0-9\u4e00-\u9fa5]*$/; // 仅允许中文、英文和数字
      if (!inputRegExp.test(userName)) {
        MessagePlugin.error('账号名称仅允许中文、英文和数字');
        return;
      }
      if (!validateContactNumber(mobile)) {
        MessagePlugin.error('手机号码格式有误');
        return;
      }
      let res = null;
      if (titleType.value === '新增') {
        res = teamAdminId ? await addTeamPlatUser(params) : await addPlatUser(params);
      } else {
        res = teamAdminId ? await updateTeamPlatUser(params) : await updatePlatUser(params);
      }
      if (res.code === 0) {
        addVisible.value = false;
        getList();
        if (res.data) {
          const { userName, passWord, userId } = res.data;
          userObj.userName = userName;
          userObj.passWord = passWord;
          userObj.userId = userId;
          userVisible.value = true;
        } else {
          MessagePlugin.success('编辑成功');
        }

        isReset.value = true;
        setTimeout(() => {
          formData.userName = '';
          formData.mobile = '';
        });
      }
    })
    .catch((error) => {
      console.log('error', error);
    });
};
// 禁用/启用
const disOrUpClick = async (row) => {
  const { teamAdminId } = route.query;
  const { teamId } = route.query;
  const { userId, status, mobile } = row;
  const params = {
    userId,
    status: status === 1 ? 0 : 1,
    mobile,
    teamId,
  };
  const res = teamAdminId ? await banTeamRolePlatUser(params) : await banPlatUser(params);
  if (res.code === 0) {
    MessagePlugin.success(status === 0 ? '已启用' : '已禁用');
    getList();
  }
};
// 重置密码
const resetClick = async (row) => {
  const res = await resetPassword({ userId: row.userId });
  if (res.code === 0) {
    MessagePlugin.success('已重置');
    const { userName, passWord } = res.data;
    userObj.userName = userName;
    userObj.passWord = passWord;
    isReset.value = false;
    userVisible.value = true;
  }
};
// 授权
const shouClick = (row) => {
  userObj.userName = row.userName;
  userObj.userId = row.userId;
  getRoleList();
  getAuthData(row.userId);
  authVisible.value = true;
};
// 获取已授权的数据
const getAuthData = async (userId) => {
  const { teamAdminId } = route.query;
  const params = {
    userId,
  };
  const res = teamAdminId ? await getUserTeamRoleRel(params) : await getUserRoleRel(params);
  if (res.code === 0) {
    roleIds.value = res.data;
  }
};
// 给用户授权
const authSubmit = async () => {
  const { teamAdminId } = route.query;
  const params = {
    userId: userObj.userId,
    roleIds: roleIds.value,
    teamAdminId,
  };
  if (!roleIds.value.length) {
    MessagePlugin.error('请选择角色');
    return;
  }
  const res = teamAdminId ? await grantTeamUser(params) : await grantUser(params);
  if (res.code === 0) {
    MessagePlugin.success('已授权');
    authVisible.value = false;
    userVisible.value = false;
    getList();
  }
};
// 复制账号密码
const copyClick = async () => {
  const textToCopy = `账号: ${userObj.userName}\n密码: ${userObj.passWord}`; // 合并账号和密码，使用换行符分隔
  await toClipboard(textToCopy); // 复制到剪贴板
  MessagePlugin.success('已复制');
};
const onClose = () => {
  addVisible.value = false;
  setTimeout(() => {
    formData.userName = '';
    formData.mobile = '';
  });
};

// 获取角色数据
const getRoleList = async () => {
  const { teamAdminId, teamId } = route.query;
  const parmas = {
    userId: userObj.userId,
    teamAdminId,
    teamId,
  };
  const res = teamAdminId ? await getTeamPlatRoleApi(parmas) : await getPlatRoleApi(parmas);
  if (res.code === 0) {
    roleData.value = res.data;
  }
};

// 获取详情
const getDetail = async (teamId) => {
  const res = await getPlatTeamById({ teamId });
  if (res.code === 0) {
    const { createUserName, teamName, roleNames } = res.data;
    infoObj.createUserName = createUserName;
    infoObj.teamName = teamName;
    infoObj.roleNames = roleNames;
  }
};
watch(
  route, // 监听 route 对象
  () => {
    getList();
    getBreadcrumb();
    const { teamId } = route.query;
    if (teamId) {
      isShow.value = true;
      getDetail(teamId);
    } else {
      isShow.value = false;
    }
  },
  // { immediate: true, deep: true }, // 初始化时立即执行一次
);

onMounted(() => {
  getRoleList();
  getList();
  getBreadcrumb();
  const { teamId } = route.query;

  if (teamId) {
    isShow.value = true;
    getDetail(teamId);
  }
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  margin-top: 20px;
}
.table-p {
  float: left;
}
.p_css {
  max-width: 400px;
}
.text_css {
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.add-css {
  float: right;
  margin-bottom: 10px;
}
// .btn-css {
//   color: #167fff;
//   cursor: pointer;
// }
</style>
