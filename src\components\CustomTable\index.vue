<template>
  <div class="custom-table-container">
    <div class="tbl-body">
      <div class="tbl-headr">
        <div>
          <Space>
            <slot name="batch"></slot>
          </Space>
        </div>
        <div>
          <Space>
            <slot name="actions"></slot>
          </Space>
        </div>
      </div>
      <Table
        sticky
        :columns="filteredColumns"
        :data-source="dataSource"
        :pagination="false"
        :row-key="rowKey"
        :loading="loading"
        :scroll="{ y: 'calc(100vh - 300px)' }"
        :expandable="{ defaultExpandAllRows: false }"
      />
    </div>
    <div class="tbl-pagination">
      <Pagination
        v-if="pagination.total > 0"
        v-model:current="localPagination.current"
        v-model:pageSize="localPagination.pageSize"
        :total="pagination.total"
        show-size-changer
        :show-total="(total) => `共 ${total} 条数据`"
        @change="handleTableChange"
        @showSizeChange="handleTableChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import type { PropType } from 'vue';
import { Table, Space, Modal, Checkbox, Row, Col, CheckboxGroup, Pagination } from 'woody-ui';

interface TablePagination {
  current?: number;
  pageSize?: number;
  total?: number;
}

// Props 定义
const props = defineProps({
  loading: {
    type: Boolean,
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  columns: {
    type: Array as PropType<any>,
    required: true,
  },
  dataSource: {
    type: Array as PropType<any[]>,
    required: true,
  },
  pagination: {
    type: Object as PropType<TablePagination>,
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
    }),
  },
});

// Emits 定义
const emit = defineEmits(['change', 'edit', 'delete']);

// 响应式数据
const columnModalVisible = ref(false);
const selectedColumns = ref<string[]>(props.columns.filter((column) => !column.isAction).map((column) => column.key));

// 本地分页数据
const localPagination = ref({ ...props.pagination });

watch(
  () => props.pagination,
  (newPagination) => {
    localPagination.value = { ...newPagination };
  },
  { deep: true },
);

// 计算属性
const filteredColumns = computed(() => {
  const actionColumns = props.columns.filter((column) => column.isAction);
  const filteredNonActionColumns = props.columns.filter(
    (column) => !column.isAction && selectedColumns.value.includes(column.key),
  );
  return [...filteredNonActionColumns, ...actionColumns];
});

const handleTableChange = (current: number, pageSize: number) => {
  emit('change', { current, pageSize });
};

// 监听 columns 变化
watch(
  () => props.columns,
  (newColumns) => {
    selectedColumns.value = newColumns.filter((column) => !column.isAction).map((column) => column.key);
  },
  { deep: true },
);

// 调试信息
console.log('selectedColumns:', selectedColumns.value);
</script>

<style scoped>
.custom-table-container {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  margin-bottom: 16px;
  display: flex;
  height: 100%;
  flex-direction: column;
  overflow: hidden;

  .tbl-headr {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    flex-shrink: 0;
  }

  .tbl-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .tbl-pagination {
    flex-shrink: 0;
    padding-top: 16px;
  }
}

/* 自定义表头样式 */
.custom-table-container :deep(.ant-table-thead th) {
  background-color: #f0f4f9;
  border-radius: 0px 0px 0px 0px;
  color: #636d7e;
}

/* 自定义表格行内上下间距 */
.custom-table-container :deep(.ant-table-tbody tr) {
  height: 48px; /* 调整行高 */
}

.custom-table-container :deep(.ant-table-tbody tr td) {
  padding: 12px 16px; /* 调整单元格内边距 */
}

/* 表格滚动样式优化 */
.custom-table-container :deep(.ant-table-body) {
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

.custom-table-container :deep(.ant-table-body::-webkit-scrollbar) {
  width: 6px;
}

.custom-table-container :deep(.ant-table-body::-webkit-scrollbar-track) {
  background: transparent;
}

.custom-table-container :deep(.ant-table-body::-webkit-scrollbar-thumb) {
  background-color: #c1c1c1;
  border-radius: 3px;
}

.custom-table-container :deep(.ant-table-body::-webkit-scrollbar-thumb:hover) {
  background-color: #a8a8a8;
}

/* 分页样式 */
.custom-table-container :deep(.ant-pagination) {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.custom-table-container :deep(.ant-pagination-total-text) {
  margin-right: 16px;
}

.custom-table-container :deep(.ant-select-selection-search-input) {
  width: 80px;
}
</style>
