import { createApp } from 'vue';
import microApp from '@micro-zoe/micro-app';

import 'tdesign-vue-next/es/style/index.css';
import 'woody-ui/dist/reset.css';
import TDesign from 'tdesign-vue-next';
import jwtDecode from 'jwt-decode';
import {
  Upload,
  Modal,
  Select,
  Button,
  Table,
  Drawer,
  Row,
  Col,
  Input,
  Form,
  DatePicker,
  InputNumber,
  Space,
  Image,
  Tooltip,
  Popconfirm,
  Switch,
  Radio,
  Carousel,
  Cascader,
  Tabs,
  TimePicker,
  Tree,
  Tag,
  Menu,
  Pagination,
  ConfigProvider,
  Descriptions,
  Checkbox,
  Popover,
  Dropdown,
  Breadcrumb,
  Divider,
} from 'woody-ui';
import { tokenStore, getDefaultTokenProvider } from '@/request/tokenProvider';
import { store } from './store';
import router from './router';
import '@/style/index.less';
import './permission';
import PageWrap from './components/PageWrap/index.vue';
import ConfirmBtn from './components/ConfirmBtn/index.vue';
import { refreshToken } from '@/api/login';
import App from './App.vue';


microApp.start();


const app = createApp(App);

app.component('PageWrap', PageWrap);
app.component('ConfirmBtn', ConfirmBtn);

app.use(TDesign);
app.use(store);
app.use(router);

app.use(Breadcrumb);

app
  .use(Upload)
  .use(Tag)
  .use(Modal)
  .use(Select)
  .use(Button)
  .use(Table)
  .use(Drawer)
  .use(Row)
  .use(Col)
  .use(Input)
  .use(Form)
  .use(InputNumber)
  .use(Space)
  .use(Image)
  .use(Tooltip)
  .use(Radio)
  .use(Popconfirm)
  .use(DatePicker)
  .use(Switch)
  .use(Carousel)
  .use(Cascader)
  .use(TimePicker)
  .use(Tree)
  .use(Tabs)
  .use(Menu)
  .use(Pagination)
  .use(ConfigProvider)
  .use(Descriptions)
  .use(Checkbox)
  .use(Popover)
  .use(Dropdown)
  .use(Divider);
app.mount('#app');

// 在主应用中暴露刷新token的方法
// window.mainAppMethod = () => {
//   const getToken = getDefaultTokenProvider();
//   return getToken;
// };

// 定义全局的跳转方法
window.navigateTo = (path, query) => {
  router.push({
    path,
    query,
  }); // 使用 Vue Router 进行路由跳转
};

// 基座刷新token逻辑
// eslint-disable-next-line consistent-return
window.mainRefreshToken = async () => {
  const token = JSON.parse(localStorage.getItem('user-auth')).accessToken;
  const getToken = JSON.parse(localStorage.getItem('user-auth')) || '';
  if (token) {
    const decoded: any = jwtDecode(token);
    const currentTime = Date.now() / 1000;
    if (decoded.exp < currentTime) {
      console.log('token过期');
      const res = await refreshToken({
        refreshToken: getToken.refreshToken,
      });
      if (res.code === 0) {
        localStorage.setItem('user-auth', JSON.stringify(res.data));
        console.log('token过期调用', res.data);
        return res.data;
      }
    }
    console.log('拿到登录时有效的token', getToken);
    return getToken;
  }
};

// 告诉 Vue 识别 'micro-app' 作为自定义元素
app.config.compilerOptions.isCustomElement = (tag) => tag === 'micro-app';
