<template>
  <div class="search-form-container">
    <Form :model="formData" layout="vertical">
      <Row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <!-- 始终显示的表单项 -->
        <template v-for="(field, index) in displayFields" :key="index">
          <Col :span="field.type === 'rangePicker' ? 12 : 6">
            <Form.Item :label="field.label">
              <component
                :is="getComponent(field.type)"
                v-model:value="formData[field.name]"
                :class="['search-form-item', { 'range-picker': field.type === 'rangePicker' }]"
                v-bind="field.props"
              >
                <template v-if="field.type === 'select'">
                  <Select.Option v-for="option in field.options" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </Select.Option>
                </template>
              </component>
            </Form.Item>
          </Col>
        </template>

        <!-- 展开后显示的表单项 -->
        <template v-if="isExpanded">
          <template v-for="(field, index) in hiddenFields" :key="'hidden-' + index">
            <Col :span="field.type === 'rangePicker' ? 12 : 6">
              <Form.Item :label="field.label">
                <component
                  :is="getComponent(field.type)"
                  v-model:value="formData[field.name]"
                  :class="['search-form-item', { 'range-picker': field.type === 'rangePicker' }]"
                  v-bind="field.props"
                >
                  <template v-if="field.type === 'select'">
                    <Select.Option v-for="option in field.options" :key="option.value" :value="option.value">
                      {{ option.label }}
                    </Select.Option>
                  </template>
                </component>
              </Form.Item>
            </Col>
          </template>
        </template>

        <!-- 操作按钮区域 -->
        <Col :span="6">
          <Form.Item class="search-buttons" label=" ">
            <Space>
              <Button type="primary" @click="handleSubmit">查询</Button>
              <Button @click="handleReset">重置</Button>
              <Button v-if="showExpand" type="link" @click="toggleExpand">
                {{ isExpanded ? '收起' : '展开' }}
                <template #icon>
                  <UpOutlined v-if="isExpanded" />
                  <DownOutlined v-else />
                </template>
              </Button>
            </Space>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { PropType } from 'vue';
import { Form, Input, Select, DatePicker, TimePicker, InputNumber, Button, Space, Row, Col } from 'woody-ui';
import { UpOutlined, DownOutlined } from '@ant-design/icons-vue';

interface FormField {
  type: string;
  label: string;
  name: string;
  props?: Record<string, any>;
  options?: Array<{ label: string; value: any }>;
}

// 组件映射表
const componentMap = {
  input: Input,
  select: Select,
  date: DatePicker,
  rangePicker: DatePicker.RangePicker,
  time: TimePicker,
  number: InputNumber,
};

// Props 定义
const props = defineProps({
  formConfig: {
    type: Array as PropType<FormField[]>,
    required: true,
    default: () => [],
  },
  column: {
    type: Number,
    default: 4,
  },
  defaultRowCount: {
    type: Number,
    default: 1,
  },
});

// Emits 定义
const emit = defineEmits(['search', 'reset']);

// 响应式数据
const formData = ref<Record<string, any>>({});
const isExpanded = ref(false);

// 显示展开按钮的条件
const showExpand = computed(() => {
  return props.formConfig.length > props.defaultRowCount * props.column - 1;
});

// 显示的表单项
const displayFields = computed(() => {
  const visibleCount = props.defaultRowCount * props.column - 1;
  return props.formConfig.slice(0, visibleCount);
});

// 隐藏的表单项
const hiddenFields = computed(() => {
  const visibleCount = props.defaultRowCount * props.column - 1;
  return props.formConfig.slice(visibleCount);
});

// 方法
const getComponent = (type: string) => {
  return componentMap[type] || Input;
};

const handleSubmit = () => {
  emit('search', formData.value);
};

const handleReset = () => {
  formData.value = {};
  emit('reset');
};

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};
</script>

<style scoped>
.search-form-container {
  background-color: #fff;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  margin: 16px 16px 16px 0;
}

.search-form-container :deep(.ant-form) {
  .ant-input,
  .ant-select,
  .ant-picker,
  .ant-input-number,
  .search-form-item {
    width: 100%;
  }
}

.search-form-container :deep(.range-picker) {
  width: 100%;
}

/* 按钮区域样式 */
.search-buttons {
  display: list-item;
}
</style>
