<template>
  <a-menu
    class="wd-menu"
    mode="inline"
    :open-keys="iconArr"
    :selected-keys="[active]"
    @open-change="menuExpand"
    @click="menuClick"
  >
    <menu-content :nav-data="list" :icon-arr="iconArr" />
  </a-menu>
</template>

<script setup lang="ts">
import { onMounted, computed, ref, watch } from 'vue';
import type { PropType } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { MenuRoute } from '@/types/interface';
import MenuContent from './MenuContent.vue';

const props = defineProps({
  menu: {
    type: Array as PropType<MenuRoute[]>,
    default: () => [],
  },
});

const route = useRoute();
const router = useRouter();
const iconArr = ref<Array<string>>([]);
type ListItemType = MenuRoute & { icon?: string };

const active = computed(() => {
  let path: any = null;
  const pathUrl = route.path;
  route.matched.forEach((item: any) => {
    if (item.path === pathUrl) {
      if (item?.meta && item?.meta?.replace) {
        const lastSlashIndex = pathUrl.lastIndexOf('/');
        path = `${pathUrl.slice(0, lastSlashIndex)}/${item.meta.replace}`;
      } else {
        path = pathUrl;
      }
    }
  });
  return path;
});

// 从路由路径中提取所有可能的父路径
const extractParentPaths = (path: string) => {
  const parts = path.split('/').filter(Boolean);
  const paths = [];
  let currentPath = '';

  for (const part of parts) {
    currentPath += `/${part}`;
    paths.push(currentPath);
  }

  return paths;
};

// 递归查找菜单中与当前路径匹配的项及其所有父项
const findMatchingMenuPaths = (menuItems: ListItemType[], currentPath: string, result: string[] = []) => {
  if (!menuItems || !menuItems.length) return result;

  for (const item of menuItems) {
    if (item.path === currentPath) {
      result.push(item.path);
      return result;
    }

    if (item.children && item.children.length) {
      const childResult = findMatchingMenuPaths(item.children, currentPath, []);
      if (childResult.length) {
        result.push(item.path);
        result.push(...childResult);
        return result;
      }
    }
  }

  return result;
};

const computeOpenKeysFromRoute = () => {
  const { path } = route;
  try {
    // 如果是系统或权限管理的首页，默认展开第一个菜单
    if (path === '/system/index' || path === '/systemManagement/index') {
      // 获取菜单列表
      const menuList = getMenuList(props.menu);

      // 如果菜单列表不为空，返回第一个菜单的路径
      if (menuList && menuList.length > 0) {
        return [menuList[0].path];
      }
    }

    // 提取当前路径的所有父路径
    const parentPaths = extractParentPaths(path);

    // 查找匹配的菜单路径
    const menuList = getMenuList(props.menu);
    const matchingPaths = findMatchingMenuPaths(menuList, path);

    // 合并结果并去重
    return [...new Set([...parentPaths, ...matchingPaths])];
  } catch (error) {
    console.error('计算展开菜单时出错:', error);
    return [];
  }
};

watch(
  () => route.path,
  (_newPath) => {
    // 路径变化时，重新计算展开的菜单
    iconArr.value = computeOpenKeysFromRoute();
  },
  { immediate: true },
);

watch(
  () => props.menu,
  () => {
    // 菜单数据变化时，重新计算展开的菜单
    iconArr.value = computeOpenKeysFromRoute();
  },
  { immediate: true },
);

const list = computed(() => {
  return getMenuList(props.menu);
});

const getMenuList = (list: MenuRoute[], basePath?: string, depth = 0): ListItemType[] => {
  if (!list || depth > 3) return [];

  list.sort((a, b) => {
    return (a.meta?.orderNo || 0) - (b.meta?.orderNo || 0);
  });

  return list
    .map((item) => {
      let { path } = item;
      if (basePath && !path.startsWith('/') && !path.includes(basePath)) {
        path = `${basePath}/${path}`;
      }

      const result = {
        path,
        title: item.meta?.title,
        icon: item.meta?.icon || '',
        activeIcon: item.meta?.activeIcon || '',
        meta: item.meta,
        children: item.children ? getMenuList(item.children, path, depth + 1) : [],
      };

      return result;
    })
    .filter((item) => item.meta && item.meta.hidden !== true);
};

const menuExpand = (openKeys: string[]) => {
  iconArr.value = openKeys;
};

const menuClick = (param) => {
  const { key } = param;
  // 确保路径不包含重复的斜杠
  const cleanPath = key.replace(/\/+/g, '/');
  console.log('菜单点击路径:', cleanPath);
  router.push({ path: cleanPath });
};

onMounted(() => {
  // 初始化逻辑
  console.log('SideNav 组件已挂载');

  // 初始化时计算展开的菜单
  iconArr.value = computeOpenKeysFromRoute();
});
</script>

<style lang="less" scoped>
.wd-menu {
  margin: 15px;
  width: 232px;
  height: calc(100vh - 92px) !important;
  border-radius: 12px;
  background: linear-gradient(to bottom, #e5f4ff, #f2effd, #f6fbfe);
  overflow-y: auto;
  overflow-x: hidden;
  border: none !important;
  margin-right: 6px;
  padding-top: 16px;
}

:deep(.t-menu) {
  border-right: 0 !important;
}

:deep(.ant-menu-inline) {
  background: none !important;
}

:deep(.ant-menu-item-selected) {
  background: #ffffff !important;
  // border-radius: 4px !important;
  font-weight: bold;
}

// 菜单样式修改
:deep(.ant-menu-submenu) {
  color: #495366 !important;
  .ant-menu-submenu-title {
    height: 48px !important;
    margin-inline: 16px;
    padding-left: 16px !important;
    padding-right: 16px !important;
    width: calc(100% - 32px);
    .ant-menu-title-content {
      margin-left: 8px !important;
    }
    &:hover {
      background-color: rgba(255, 255, 255, 0.6) !important;
    }
  }
  .ant-menu-item {
    padding-left: 16px !important;
    height: 48px !important;
    margin-inline: 16px;
    width: calc(100% - 32px);
    &:not(.ant-menu-item-selected):hover {
      background-color: rgba(255, 255, 255, 0.6);
    }
  }
  .ant-menu-submenu {
    .ant-menu-submenu-title {
      padding-left: 16px !important;
    }
    .ant-menu-item {
      padding-left: 41px !important;
      height: 48px !important;
      margin-inline: 16px;
      width: calc(100% - 32px);
      &:not(.ant-menu-item-selected):hover {
        background-color: rgba(255, 255, 255, 0.6);
      }
    }
  }
  .ant-menu-item-icon {
    margin-right: 0;
  }
}

:deep(.ant-menu-submenu-selected) {
  > .ant-menu-submenu-title {
    color: #1677ff !important;
    font-weight: bold;
  }
}

// 滚动条整体样式
.ant-menu::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度 */
}
/* 滚动条轨道样式 */
.ant-menu::-webkit-scrollbar-track {
  // background-color: #f1f6f8; /* 轨道背景色 */
  border-radius: 3px; /* 圆角 */
}

/* 滚动条滑块样式 */
.ant-menu::-webkit-scrollbar-thumb {
  background-color: #e0e8ed; /* 滑块颜色 */
  border-radius: 3px; /* 圆角 */
}
</style>
