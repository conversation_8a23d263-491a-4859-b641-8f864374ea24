<template>
  <div>
    <div class="header">
      <div class="header-logo-container">
        <img class="logo big" src="@/assets/images/big-logo.png" alt="" />
      </div>
      <nav-tab class="nav-css"></nav-tab>
      <div class="user-box">
        <img src="../../assets/images/user.png" class="user-icon" />
        <a-dropdown :trigger="['click']">
          <a class="header-user-account">
            <span style="padding-right: 15px">{{ userName }}</span>
            <down-outlined />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item key="0" @click.prevent="handleLogout">
                <span><poweroff-outlined style="padding-right: 8px" />退出登录</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
// import type { PropType } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { DownOutlined, PoweroffOutlined } from '@ant-design/icons-vue';
import { useSettingStore, useUserStore } from '@/store';
import { getActive } from '@/router';
// import { prefix } from '@/config/global';
// import type { MenuRoute } from '@/types/interface';
// import MenuContent from './MenuContent.vue';
import NavTab from './NavTab.vue';

// const props = defineProps({
//   theme: {
//     type: String,
//     default: '',
//   },
//   layout: {
//     type: String,
//     default: 'top',
//   },
//   showLogo: {
//     type: Boolean,
//     default: true,
//   },
//   menu: {
//     type: Array as PropType<MenuRoute[]>,
//     default: () => [],
//   },
//   isFixed: {
//     type: Boolean,
//     default: false,
//   },
//   isCompact: {
//     type: Boolean,
//     default: false,
//   },
//   maxLevel: {
//     type: Number,
//     default: 3,
//   },
// });

const userStore = useUserStore();
console.log(userStore, 'userStore');
const userName = ref('');
// const userName = computed(() => {
//   console.log(localStorage.getItem('user-auth'), 'ppp');
//   // const userObj = JSON.parse()
//   // userStore.userName ? userStore.userName : '';
// });

const router = useRouter();
const settingStore = useSettingStore();

const toggleSettingPanel = () => {
  settingStore.updateConfig({
    showSettingPanel: true,
  });
};

const route = useRoute();
const active = computed(() => getActive(3, route));

// const layoutCls = computed(() => [`${prefix}-header-layout`]);

// const menuCls = computed(() => {
//   const { isFixed, layout, isCompact } = props;
//   return [
//     {
//       [`${prefix}-header-menu`]: !isFixed,
//       [`${prefix}-header-menu-fixed`]: isFixed,
//       [`${prefix}-header-menu-fixed-side`]: layout === 'side' && isFixed,
//       [`${prefix}-header-menu-fixed-side-compact`]: layout === 'side' && isFixed && isCompact,
//     },
//   ];
// });

// const changeCollapsed = () => {
//   settingStore.updateConfig({
//     isSidebarCompact: !settingStore.isSidebarCompact,
//   });
// };

const handleLogout = () => {
  localStorage.removeItem('user');
  localStorage.removeItem('user-auth');
  localStorage.removeItem('menuList');
  router.push({
    path: '/login',
    query: { redirect: encodeURIComponent(router.currentRoute.value.fullPath) },
  });
};
onMounted(() => {
  const userAuth = JSON.parse(localStorage.getItem('user-auth'));
  userName.value = userStore.userName || userAuth.userName;
});
</script>
<style lang="less" scoped>
.header {
  height: 72px;
  background: #197af8;
  overflow: hidden;
  padding-top: 10px;
  .header-logo-container {
    float: left;
    .logo {
      height: 50px;
      width: 157px;
      margin-left: 52px;
    }
  }

  .nav-css {
    float: left;
    padding-top: 8px;
  }
  .user-box {
    float: right;
    padding-right: 50px;
    padding-top: 7px;
    .user-icon {
      width: 38px;
      height: 38px;
      margin-right: 15px;
    }
    .header-user-account {
      color: #ffffff;
      font-size: 16px;
    }
  }
}
</style>
