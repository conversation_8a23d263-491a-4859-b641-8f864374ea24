import { defineStore } from 'pinia';
import { store } from '@/store';
import { tokenStore } from '@/request/tokenProvider';
import { accountLogin } from '@/api/login';
import microApp from '@micro-zoe/micro-app';

interface LoginRequestParam {
  validate: string;
  password: string;
  userName: string;
}

export const useUserStore = defineStore('user-info', {
  state: () => ({
    roles: [],
    permissions: [],
    userName: '',
  }),
  actions: {
    async login(loginData: LoginRequestParam): Promise<any> {
      const res = await accountLogin(loginData);
      if (res.code === 0) {
        this.userName = loginData.userName;
        res.data.userName = loginData.userName;

        localStorage.setItem('user-auth', JSON.stringify(res.data));
        microApp.setData('settledIndex', { userAuth: res.data }); // 传入子应用
        microApp.setData('market', { userAuth: res.data });
        tokenStore.setToken(res.data.accessToken);
        tokenStore.setRefreshToken(res.data.refreshToken);
      }
      return Promise.resolve(res);
    },
    async getUserInfo() {
      const data = {
        roles: ['all', 'admin'],
        permissions: ['shopCenter', 'shopList', 'orderCenter', 'orderList'],
      };

      await new Promise((resolve) => {
        setTimeout(() => {
          resolve(data);
        }, 10);
      });
      this.roles = data.roles;
      this.permissions = data.permissions;

      return {
        roles: this.roles,
        permissions: this.permissions,
      };
    },
    async logout() {
      localStorage.removeItem('user-auth');
      localStorage.removeItem('menuList');

      localStorage.removeItem('setting');
      tokenStore.setToken('');
      tokenStore.setRefreshToken('');
      this.userName = '';
      this.roles = [];
      this.permissions = [];
    },
  },
  // persist: {},
});

export function getUserStore() {
  return useUserStore(store);
}
