// 手机号码校验
export function validateContactNumber(number) {
  const phonePattern = /^1[3-9]\d{9}$/; // 手机号码
  return phonePattern.test(number);
}

// 密码校验
export function validatePassword(password) {
  // 正则表达式规则
  const lengthRegex = /^.{8,30}$/; // 长度在 8-30 之间
  const uppercaseRegex = /[A-Z]/; // 至少包含一个大写字母
  const lowercaseRegex = /[a-z]/; // 至少包含一个小写字母
  const numberRegex = /[0-9]/; // 至少包含一个数字
  const chineseOrSymbolRegex = /[\u4e00-\u9fa5\uFF00-\uFFFF]/; // 中文字符或全角符号

  // 校验
  if (!lengthRegex.test(password)) {
    return {
      status: false,
      message: '密码长度必须在 8 到 30 个字符之间',
    };
  }
  if (!uppercaseRegex.test(password)) {
    return {
      status: false,
      message: '密码必须包含至少一个大写字母',
    };
  }
  if (!lowercaseRegex.test(password)) {
    return {
      status: false,
      message: '密码必须包含至少一个小写字母',
    };
  }
  if (!numberRegex.test(password)) {
    return {
      status: false,
      message: '密码必须包含至少一个数字',
    };
  }
  if (chineseOrSymbolRegex.test(password)) {
    return {
      status: false,
      message: '密码不能包含中文或中文符号',
    };
  }
  return {
    status: true,
  };
}
