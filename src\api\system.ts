import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';
import { Response } from './common';

export interface ResponseData<T> {
  code: number;
  message: string;
  data: T;
}

export interface LoginRequestParam {
  userName: string;
  password: string;
}

export interface resetParam {
  userName: string;
  passWord: string;
  userId: string;
}

export interface LoginResponseData {
  roleName: any;
  accessToken: string;
  refreshToken: string;
}

export interface Pagination {
  current?: number;
  size: number;
  total?: number;
  pages?: number;
  page?: number;
  records?: any;
}

interface Role {
  roleId: string;
  roleName: string;
  [key: string]: any;
}

export interface TreeData {
  npPlatMenu: any[];
  platMenu: any[];
  jicaiMenu: any[];
  // newPlatMenu: any[];
  // oldPlatMenu: any[];
}

export interface roleByIdData {
  remark: string;
  roleName: string;
  menuIds: any[];
  npMenuIds: any[];
  jicaiMenuIds: any[];
}
type roleResData = Role[];

// 获取菜单树形结构
export const getPlatMenuTreeList = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<TreeData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-role/getPlatMenuTreeList',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 查询角色列表

export const getPlatRoleList = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<Pagination>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-role/getPlatRoleListPage',
    data,
    includeCredentials: true,
    tokenProvider,
  });
// 获取可选的角色

export const getPlatRoleApi = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<roleResData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-user/getPlatRoleList',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 新增成员
export const addPlatRole = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-role/addPlatRole',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 查看详情
export const getPlatRoleById = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<roleByIdData>>({
    method: 'GET',
    path: `/life-platform-dashboard/plat-role/getPlatRoleById?roleId=${data.roleId}`,
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 修改角色信息
export const updatePlatRole = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-role/updatePlatRole',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 禁用/启用角色
export const banRolePlatUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-role/banPlatUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 账号--新增成员
export const addPlatUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<resetParam>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-user/addPlatUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 账号--分页查询
export const getPlatUserList = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<Pagination>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-user/getPlatUserListPage',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 账号--查看详情
export const getPlatUserById = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'GET',
    path: '/life-platform-dashboard/plat-user/getPlatUserById',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 账号--编辑
export const updatePlatUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-user/updatePlatUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 账号--禁用/启用成员
export const banPlatUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-user/banPlatUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 账号--重置密码
export const resetPassword = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<resetParam>>({
    method: 'GET',
    path: `/life-platform-dashboard/plat-user/resetPassword?userId=${data.userId}`,
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 账号--给用户授权
export const grantUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-user/grantUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 账号--获取用户已经绑定的角色
export const getUserRoleRel = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<roleResData>>({
    method: 'GET',
    path: `/life-platform-dashboard/plat-user/getUserRoleRel?userId=${data.userId}`,
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--查看详情
export const getPlatTeamById = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'GET',
    path: `/life-platform-dashboard/plat-team/getPlatTeamById?teamId=${data.teamId}`,
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--编辑
export const updatePlatTeam = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team/updatePlatTeam',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--新增团队
export const addPlatTeam = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team/addPlatTeam',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--分页查询
export const getPlatTeamListPage = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<Pagination>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team/getPlatTeamListPage',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--成员--新增成员
export const addTeamPlatUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<resetParam>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team-user/addPlatUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--成员--列表查询
export const getTeamPlatUserList = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<Pagination>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team-user/getPlatUserListPage',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--成员--获取可选的角色
export const getTeamPlatRoleApi = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<roleResData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team-user/getPlatRoleList',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--成员--给用户授权
export const grantTeamUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team-user/grantUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--成员--查看成员
export const getTeamPlatUserById = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'GET',
    path: '/life-platform-dashboard/plat-team-user/getPlatUserById',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--成员--编辑成员
export const updateTeamPlatUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team-user/updatePlatUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--成员--禁用/启用成员
export const banTeamRolePlatUser = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-team-user/banPlatUser',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 团队--成员--获取用户已经绑定的角色
export const getUserTeamRoleRel = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<roleResData>>({
    method: 'GET',
    path: `/life-platform-dashboard/plat-team-user/getUserRoleRel?userId=${data.userId}`,
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 获取当前用户可分配权限

export const getCurrentAssignMenus = (data: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<TreeData>>({
    method: 'GET',
    path: `/life-platform-dashboard/plat-user/getCurrentAssignMenus`,
    data,
    includeCredentials: true,
    tokenProvider,
  });
