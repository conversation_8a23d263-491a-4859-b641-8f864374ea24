<template>
  <a-form ref="formRef" :model="formData" :layout="layout" name="basic" class="form-css" @finish="onSearch">
    <a-row :key="rowIndex" :gutter="24" :style="{ marginBottom: expand && formList.length % 4 === 0 ? 0 : '-24px' }">
      <a-col
        v-for="(item, rowIndex) in formList"
        v-show="expand || rowIndex <= 6"
        :key="item.name"
        :span="layout === 'horizontal' ? 24 : item.span || 6"
      >
        <a-form-item
          :label="item.label"
          :class="{ rangeInput: item.type === 'rangeInput' }"
          :name="item.name"
          :extra="item.extra"
        >
          <a-select
            v-if="item.type === 'select'"
            v-model:value="formData[item.name]"
            :show-search="item.showSearch"
            :show-arrow="!item.showSearch"
            :placeholder="item.placeholder ? item.placeholder : '请选择内容'"
            :mode="item.mode"
            :field-names="{
              label: item.labelKey || 'label',
              value: item.valueKey || 'value',
            }"
            :max-tag-count="2"
            allow-clear
            :filter-option="(input, option) => selectFilter(input, option, item)"
            :options="remoteOptions[item.name] || item.options"
            @focus="selectFocus(item)"
          >
          </a-select>
          <a-date-picker
            v-else-if="item.type === 'datePicker'"
            v-model:value="formData[item.name]"
            :format="item.showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'"
            :valueFormat="item.showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'"
            allowClear
            :showTime="item.showTime"
          />
          <a-range-picker
            v-else-if="item.type === 'rangePicker'"
            :format="item.showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'"
            :valueFormat="item.showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'"
            v-model:value="formData[item.name]"
            :disabled-date="(current) => disabledDate(current, item)"
            allowClear
            :showTime="item.showTime"
            :placeholder="item.placeholder ? item.placeholder : ['开始时间', '结束时间']"
            @calendarChange="onCalendarChange"
          />
          <a-input-number
            v-else-if="item.type === 'inputNumber'"
            v-model:value="formData[item.name]"
            placeholder="请输入内容"
            :min="item.min"
            :max="item.max"
            allowClear
          />
          <a-input-group v-else-if="item.type === 'rangeInput'" style="width: 100%" class="66665565" compact>
            <a-input-number
              v-model:value="formData[item.name[0]]"
              :min="0"
              :max="item.max || 9999999"
              style="width: 45%"
              placeholder="请输入"
            />
            <a-input
              class="site-input-split"
              style="width: 10%; border-left: 0; pointer-events: none; text-align: center"
              placeholder="—"
              disabled
            />
            <a-input-number
              v-model:value="formData[item.name[1]]"
              :min="0"
              :max="item.max || 9999999"
              class="site-input-right"
              style="width: 45%"
              placeholder="请输入"
            />
          </a-input-group>
          <a-input-password v-else-if="item.type === 'inputPassword'" v-model:value="formData[item.name]" />
          <a-switch v-else-if="item.type === 'switch'" v-model:checked="formData[item.name]" />
          <a-checkbox-group v-else-if="item.type === 'checkbox'" v-model:value="formData[item.name]">
            <a-checkbox v-for="(items, index) in item.options" :value="items.value" :key="index">{{
              items.label
            }}</a-checkbox>
          </a-checkbox-group>
          <a-radio-group v-else-if="item.type === 'radio'" v-model:value="formData[item.name]">
            <a-radio v-for="(items, index) in item.options" :value="items.value" :key="index">{{
              items.label
            }}</a-radio>
          </a-radio-group>
          <a-textarea v-else-if="item.type === 'textarea'" v-model:value="formData[item.name]" />
          <a-upload v-else-if="item.type === 'upload'" :action="item.url" list-type="picture-card">
            <div>
              <PlusOutlined />
              <div style="margin-top: 8px">上传</div>
            </div>
          </a-upload>
          <a-cascader
            v-else-if="item.type === 'cascader'"
            v-model:value="formData[item.name]"
            :change-on-select="item.changeOnSelect"
            :options="isOptions(item) ? item.options : remoteOptions[item.name]"
            :field-names="{
              label: item.labelKey || 'label',
              value: item.valueKey || 'value',
              children: item.childrenKey || 'children',
            }"
            :multiple="item.multiple"
            max-tag-count="responsive"
            :show-checked-strategy="Cascader.SHOW_CHILD"
            :placeholder="item.placeholder ? item.placeholder : '请选择'"
          />
          <!-- <a-cascader
            v-else-if="item.type === 'cascader'"
            v-model="selectedOptions"
            :options="item.options"
            :load-data="cascaderLoadData"
            :field-names="{
              label: item.labelKey || 'label',
              value: item.valueKey || 'value',
            }"
            placeholder="请选择"
            @change="handleChange"
          /> -->
          <div v-else-if="item.type === 'custom'">
            <slot :name="item.name" v-bind="{ slotChange, formData }"></slot>
          </div>
          <a-input
            v-else
            v-model:value="formData[item.name]"
            placeholder="请输入内容"
            :maxlength="item.maxlength"
            allowClear
            :suffix="item.isSuffix || ''"
          />
        </a-form-item>
      </a-col>
      <a-col v-if="layout === 'horizontal'" :span="24">
        <a-form-item :wrapper-col="wrapperCol" style="text-align: center">
          <a-button htmlType="submit" type="primary">保存</a-button>
          <a-button style="margin-left: 16px" @click="cancelFunc" type="default">取消</a-button>
        </a-form-item>
      </a-col>
      <a-col :span="6" v-else class="btn-container">
        <a-form-item :wrapper-col="wrapperCol">
          <a-button htmlType="submit" type="primary">查询</a-button>
          <a-button class="ml16" @click="resetFunc" type="default">重置</a-button>

          <a
            v-if="formList.length > 7"
            style="font-size: 14px; margin-left: 16px; color: #1677ff"
            @click="expand = !expand"
          >
            {{ expand ? '收起' : '展开' }}
            <template v-if="expand">
              <UpOutlined />
            </template>
            <template v-else>
              <DownOutlined />
            </template>
          </a>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>
<script setup>
import { reactive, ref, watch, onMounted } from 'vue';
import { Cascader } from 'woody-ui';
import { DownOutlined, UpOutlined, PlusOutlined } from '@ant-design/icons-vue';
import fetchCategory from './queryCategory';
import { queryCategory } from '@/api/common';
import dayjs from 'dayjs';

const wrapperCol = { span: 24, offset: 0 };
const formData = ref({});
const expand = ref(false);
const remoteOptions = reactive({});
const rangeDate = ref();
const formRef = ref(null);

const selectedOptions = ref([]);
const cascaderOpt = ref([]);

const props = defineProps({
  formList: {
    type: Array,
    default: () => [],
  },
  layout: {
    type: String,
    default: 'vertical',
  },
  options: {
    type: Array,
    default: () => [],
  },
  colSpan: {
    type: Number,
    default: 6,
  },
  showDownload: {
    type: Boolean,
    default: false,
  },
  isAloneRowBtn: {
    type: Boolean,
    default: false,
  },
});
onMounted(() => {
  //   fetchOptions();
  // queryCategory();
  // if (props.layout) {
  //   expand.value = true;
  // }
  props.formList.map(async (item) => {
    if (item.searchFn === 'common') {
      if (item.hideAll) {
        let res = await queryCategory();
        remoteOptions[item.name] = res.data;
      } else {
        remoteOptions[item.name] = await fetchCategory();
      }
    }
  });
});

// const fetchOptions = async (id = '') => {
//   const res = await GetCommodityList({categoryId:id});
//   console.log(res, "res123");
//   if (res.code === 0) {
//     cascaderOpt.value = res.data;
//     // if(id){
//     //     targetOption.children = res.data
//     // }
//   }
//   cascaderOpt.value = [...cascaderOpt.value]
//   console.log(cascaderOpt.value, "666");
// };

// 事件收集
const emits = defineEmits(['onSearch', 'back', 'cascader-change', 'export-click', 'onReset']);
// 点击查询、保存
const onSearch = () => {
  // console.log(formData.value, "9595");
  const params = {};
  Object.keys(formData.value).forEach((key) => {
    // if (Array.isArray(formData.value[key])) {
    //   // if (0 in formData.value[key]) {
    //   // 处理数组类型，比如时间范围选择。
    //   params[key] = formData.value[key];
    //   // }
    // } else {
    //   params[key] = formData.value[key];
    // }
    if (formData.value[key] || formData.value[key] === 0 || formData.value[key] === false) {
      if (Array.isArray(formData.value[key]) && formData.value[key].length === 0) {
        params[key] = undefined;
      } else {
        params[key] = formData.value[key];
      }
    }
    console.log(params, 'para666');
  });
  emits('onSearch', params);
};
// 重置
const resetFunc = () => {
  formData.value = {};
  emits('onReset');
  onSearch();
};
// 只重置不查询
const resetOnlyFunc = () => {
  formData.value = {};
};
// 取消
const cancelFunc = () => {
  emits('back');
};
// 处理下拉框数据过滤
const selectFilter = (input, option, target) => {
  console.log(option, target, 'target');
  if (!target.needFilter) return false;
  // console.log(option[option.labelKey || option.label]);
  return (option.label || option.labelKey).includes(input);
};
// select 聚焦
const selectFocus = async (item) => {
  // console.log(6565);
  if (item.searchFn) {
    const data = await item.searchFn();
    remoteOptions[item.name] = data;
  } else {
    remoteOptions[item.name] = item.options;
  }
};
// 日期插件禁用功能
const disabledDate = (current, param) => {
  // console.log(param, "param");
  // console.log(current, "cuur");
  if (!param.limit) {
    return false;
  }
  if (!rangeDate.value || rangeDate.value.length === 0) {
    return false;
  }
  const tooLate = rangeDate.value[0] && current.diff(rangeDate.value[0], 'days') > param.limit;
  const tooEarly = rangeDate.value[1] && rangeDate.value[1].diff(current, 'days') > param.limit;
  return tooEarly || tooLate;
};

const onCalendarChange = (val) => {
  if (val && val.length) {
    let arr = [];
    val.forEach((item) => {
      if (item) {
        arr.push(dayjs(item));
      }
    });
    rangeDate.value = arr;
  }
};

// 是否有options
const isOptions = (obj) => Array.isArray(obj.options) && 0 in obj.options;
// // 级联组件加载数据
// const cascaderLoadData = async (selectedOptions, obj) => {
//   if (obj.searchFn !== "common") {
//     const targetOption = selectedOptions[selectedOptions.length - 1];
//     const tempdata = await obj.searchFn(targetOption[obj.valueKey]);
//     if (0 in tempdata) {
//       targetOption.children = tempdata;
//     }
//   }
// };

// 级联选择值切换时逻辑
// const handleChange = async (value, selectedOptions, obj) => {
//     console.log(value, selectedOptions, obj,'777')
//   const targetOption = selectedOptions[selectedOptions.length - 1];
//   const targetItem = Array.isArray(targetOption) ? targetOption[0] : {};
//   const key = targetItem.valueKey;
//   const itemName = targetItem.formItem;
//   remoteOptions[itemName] = [];

//   if (
//     Array.isArray(targetItem.children) &&
//     !(0 in targetItem.children) &&
//     !targetItem.noChildren
//   ) {
//     // 子元素没加在过，并且有子元素
//     // const tempdata = await requestApi[targetItem.requestName](targetItem[targetItem.valueKey]);
//     console.log(targetItem[targetItem.valueKey],'889999')
//     const tempdata = await obj.searchFn(targetItem[targetItem.valueKey]);
//     if (Array.isArray(tempdata) && 0 in tempdata) {
//       targetItem.children = tempdata;
//       const arr = [];
//       tempdata.forEach((item) => {
//         arr.push([targetItem[key], item[key]]);
//         remoteOptions[itemName].push(item[key]);
//       });
//       formData.value[itemName].forEach((item) => {
//         if (item.length > 1) {
//           arr.push(item);
//           remoteOptions[itemName].push(item[item.length - 1]);
//         }
//       });
//       formData.value[itemName] = arr;
//     } else {
//       targetItem.noChildren = true;
//     }
//   } else if (Array.isArray(formData.value[itemName])) {
//     console.log('880')
//     formData.value[itemName].forEach((item) => {
//       remoteOptions[itemName].push(item[item.length - 1]);
//     });
//   }
// };

// const handleChange = (value) => {
//   console.log("Selected:", value);
//   fetchOptions(value[0])
//   emits("cascader-change",value[0])

// };

// 插槽事件
const slotChange = (info, name) => {
  // console.log(info, "info");
  formData.value[name] = info;
};
watch(
  () => props.formList,
  (newValue) => {
    console.log(newValue, 'newValue');
    if (Array.isArray(newValue)) {
      newValue.forEach(async (item) => {
        if (item.defaultValue) {
          formData.value[item.name] = item.defaultValue;
        } else if (item.type === 'rangePicker' || (item.type === 'select' && item.multiple)) {
          formData.value[item.name] = [];
          if (item.type === 'rangePicker') {
            formData.value[item.name] = [];
          }
        } else if (Array.isArray(item.name)) {
          formData.value[item.name[0]] = undefined;
          formData.value[item.name[1]] = undefined;
        } else if (item.type === 'cascader' && item.searchFn && item.searchFn !== 'common') {
          remoteOptions[item.name] = await item.searchFn();
        } else {
          formData.value[item.name] = undefined;
        }
      });
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

defineExpose({
  formRef,
  // handleClearAll,
  resetOnlyFunc,
});
</script>
<style lang="less" scoped>
.ml16 {
  margin-left: 16px;
}
.form-css {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
}
.ant-form {
  :deep(.rangeInput) {
    > div {
      > div:last-child {
        flex: 1 !important;
      }
    }
  }
}
.btn-container {
  // margin-bottom: 0;
  display: flex;
  align-items: center;
  .ant-form-item {
    margin-bottom: 0;
  }
}
.ant-picker,
.ant-input-number {
  width: 100%;
}
</style>
