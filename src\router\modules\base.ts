import Layout from '@/layouts/index.vue';
import DashboardIcon from '@/assets/assets-slide-dashboard.svg';
// import DetailIcon from '@/assets/assets-slide-detail.svg';
import systemLayout from '@/pages/system/index.vue';
import ResourceManagementIcon from '@/assets/images/resourceManagement.svg?url';
import ResourceManagementActiveIcon from '@/assets/images/resourceManagement-active.svg?url';
import MenuManangmentIcon from '@/assets/images/menuManangment.svg?url';
import MenuManangmentActiveIcon from '@/assets/images/menuManangment-active.svg?url';
import TeamManangmentIcon from '@/assets/images/teamManangment.svg?url';
import TeamManangmentActiveIcon from '@/assets/images/teamManangment-active.svg?url';
import AdminManangmentIcon from '@/assets/images/adminManangment.svg?url';
import AdminManangmentActiveIcon from '@/assets/images/adminManangment-active.svg?url';

export default [
  {
    path: '/index',
    component: Layout,
    name: 'index',
    meta: { title: '首页', icon: DashboardIcon, single: true, hidden: true },
    children: [
      {
        path: 'index',
        name: 'indexIndex',
        component: () => import('@/pages/index/index.vue'),
        meta: { title: '首页', expanded: true },
      },
    ],
  },
  {
    path: '/settled',
    component: Layout,
    name: 'settled',
    meta: { title: '入驻', icon: DashboardIcon, single: true },
    children: [
      {
        path: 'index',
        name: 'settledIndex',
        component: () => import('@/pages/settled/index.vue'),
        meta: { title: '入驻', expanded: true },
      },
    ],
  },
  {
    path: '/market',
    component: Layout,
    name: 'market',
    meta: { title: '营销', icon: DashboardIcon, single: true },
    children: [
      {
        path: 'index',
        name: 'marketIndex',
        component: () => import('@/pages/market/index.vue'),
        meta: { title: '营销', expanded: true },
      },
    ],
  },
  {
    path: '/user',
    component: Layout,
    name: 'user',
    meta: { title: '用户', icon: DashboardIcon, single: true },
    children: [
      {
        path: 'index',
        name: 'userIndex',
        component: () => import('@/pages/user/index.vue'),
        meta: { title: '用户', expanded: true },
      },
    ],
  },
  {
    path: '/trade',
    component: Layout,
    name: 'trade',
    meta: { title: '交易', icon: DashboardIcon, single: true },
    children: [
      {
        path: 'index',
        name: 'tradeIndex',
        component: () => import('@/pages/trade/index.vue'),
        meta: { title: '交易', expanded: true },
      },
    ],
  },
  {
    path: '/data',
    component: Layout,
    name: 'data',
    meta: { title: '数据', icon: DashboardIcon, single: true },
    children: [
      {
        path: 'index',
        name: 'dataIndex',
        component: () => import('@/pages/data/index.vue'),
        meta: { title: '数据', expanded: true },
      },
    ],
  },
  // 2025.5.28 先隐藏老平台端菜单
  // {
  //   path: '/oldPlatform',
  //   component: Layout,
  //   name: 'oldPlatform',
  //   meta: { title: '老平台端', icon: DashboardIcon, single: true },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'oldPlatformIndex',
  //       component: () => import('@/pages/oldPlatform/index.vue'),
  //       meta: { title: '老平台端', expanded: true },
  //     },
  //   ],
  // },
  {
    // 系统
    path: '/system',
    component: Layout,
    name: 'system',
    meta: { title: '系统', icon: DashboardIcon },
    nameEn: 'npSystem',
    children: [
      {
        path: '/system/index',
        name: 'systemIndex',
        component: systemLayout,
        meta: { title: '系统设置', expanded: true },
        // children: [
        //   {
        //     path: '/system/sysSetting/menuMaint',
        //     name: 'systemMenu',
        //     component: () => import('@/pages/system/sysSetting/menuMaint.vue'),
        //     meta: { title: '菜单管理', expanded: true },
        //   },
        // ],
      },
      {
        path: 'index',
        name: 'resourcesIndex',
        nameEn: 'resourceManagement',
        component: systemLayout,
        meta: {
          title: '资源管理',
          expanded: true,
          icon: ResourceManagementIcon,
          activeIcon: ResourceManagementActiveIcon,
          state: 'IS_SHOW',
        },
        children: [
          {
            path: '/resources/picture',
            name: 'npPictureManangment',
            nameEn: 'npPictureManangment',
            component: () => import('@/pages/system/resources/picture/index.vue'),
            meta: { title: '图片管理', expanded: false, state: 'IS_SPOT' },
            children: [],
          },
        ],
      },

      {
        path: 'menu-manager',
        name: 'menuManangment',
        nameEn: 'npMenuManangment',
        component: systemLayout,
        meta: {
          title: '菜单管理',
          expanded: true,
          icon: MenuManangmentIcon,
          activeIcon: MenuManangmentActiveIcon,
          state: 'IS_SHOW',
        },
        children: [
          {
            path: '/menu-manager/merchantmenu',
            name: 'merchantmenu',
            nameEn: 'npMerchantMenuManagement',
            component: () => import('@/pages/system/merchantMenu/index.vue'),
            meta: { title: '商家端菜单管理', expanded: false, state: 'IS_SPOT' },
            children: [],
          },
        ],
      },
    ],
  },
  // 权限
  {
    path: '/systemManagement',
    component: Layout,
    name: 'systemManagement',
    nameEn: 'npSystemManagement',
    meta: { title: '权限', icon: DashboardIcon },
    children: [
      {
        path: '/systemManagement/index',
        name: 'systemManagementIndex',
        nameEn: 'npSystemManagementIndex',
        component: systemLayout,
        meta: { title: '系统设置', expanded: true, state: 'IS_SPOT' },
        // children: [
        //   {
        //     path: '/systemManagement/sysSetting/menuMaint',
        //     name: 'systemMenu',
        //     component: () => import('@/pages/system/sysSetting/menuMaint.vue'),
        //     meta: { title: '菜单管理', expanded: true },
        //   },
        // ],
      },
      {
        path: 'index',
        name: 'adminManangment',
        nameEn: 'npAdminManangment',
        component: systemLayout,
        meta: {
          title: '管理员管理',
          expanded: true,
          icon: AdminManangmentIcon,
          activeIcon: AdminManangmentActiveIcon,
          state: 'IS_SHOW',
        },
        children: [
          {
            path: '/adminManangment/countManangment',
            name: 'countManangment',
            component: () => import('@/pages/system/sysAdmin/countMaint.vue'),
            meta: { title: '账号管理', expanded: true, state: 'IS_SPOT' },
            nameEn: 'npCountManangment',
          },
          {
            path: '/adminManangment/roleManangment',
            name: 'adminManangment',
            component: () => import('@/pages/system/sysAdmin/roleMaint.vue'),
            meta: { title: '角色管理', expanded: true, state: 'IS_SPOT' },
            nameEn: 'npRoleManangment',
          },
        ],
      },
      {
        path: 'teamManangment',
        name: 'teamManangment',
        component: systemLayout,
        meta: {
          title: '团队管理',
          expanded: true,
          icon: TeamManangmentIcon,
          activeIcon: TeamManangmentActiveIcon,
          state: 'IS_SHOW',
        },
        nameEn: 'npTeamManangment',
        children: [
          {
            path: '/teamManangment/teamList',
            name: 'teamList',
            nameEn: 'npTeamList',
            component: () => import('@/pages/system/teamMaint/teamList.vue'),
            meta: { title: '团队列表', expanded: true, state: 'IS_SPOT' },
          },
        ],
      },
    ],
  },
];
