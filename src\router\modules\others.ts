// import Layout from '@/layouts/index.vue';

export default [
  // {
  //   path: '/user',
  //   name: 'user',
  //   component: Layout,
  //   redirect: '/user/index',
  //   meta: { title: '个人页', icon: 'user-circle' },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'UserIndex',
  //       component: () => import('@/pages/user/index.vue'),
  //       meta: { title: '个人中心' },
  //     },
  //   ],
  // },
];
