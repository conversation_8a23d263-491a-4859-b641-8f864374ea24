export const PIC_PREFIX = 'https://img4a.shwoody.com'; // 图片前缀
export const PIC_TAG = '/-url-'; // 图片地址占位标签
export const prefix = 'tdesign-starter';
export const TOKEN_NAME = 'tdesign-starter';
export const { VITE_DOWNLOAD_CMS_BIND_PROD } = import.meta.env; // cms-前台分类-批量添加分类商品模板下载地址

// 店铺状态枚举
export const SHOP_STATUS = [
  { label: '停业', value: 0 },
  { label: '开业', value: 1 },
  { label: '平台下线', value: 2 },
  { label: '店铺封禁', value: 3 },
];

// 店铺状态枚举扩展成 'key: value' 形式
export const SHOP_STATUS_EXPEND = {};
SHOP_STATUS.forEach((item) => {
  SHOP_STATUS_EXPEND[item.value] = item.label;
});

// 审核信息枚举
export const APPROVE_MSG = [
  { label: '全部', value: '' },
  { label: '企业认证信息', value: 'ENTERPRISE' },
  { label: '店铺信息', value: 'SHOP' },
  { label: '企业资质', value: 'QUALIFICATIONS' },
];

// 审核信息枚举扩展 'key: value' 形式
export const APPROVE_MSG_EXPEND = {};
APPROVE_MSG.forEach((item) => {
  APPROVE_MSG_EXPEND[item.value] = item.label;
});
