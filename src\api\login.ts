import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';
import { Response } from './common';

export interface LoginRequestParam {
  userName: string;
  password: string;
  validate: string;
}

export interface LoginResponseData {
  accessToken: string;
  refreshToken: string;
  userName: string;
}

export interface modifyData {}

export interface Menudata {
  hideIndex?: boolean;
  menuList?: any;
}

export const accountLogin = (data: LoginRequestParam) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-user/login',
    data,
    includeCredentials: true,
  });

export const refreshToken = (tokenProvider = getDefaultTokenProvider()) =>
  request<Response<Pick<LoginResponseData, 'accessToken' | 'refreshToken'>>>({
    method: 'GET',
    path: `/life-platform-dashboard/plat-user/refreshToken?refreshToken=${tokenProvider.refreshToken}`,
    includeCredentials: true,
  });

// 获取当前用户权限
export const getCurrentMenus = (data, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<Menudata>>({
    method: 'GET',
    path: '/life-platform-dashboard/plat-user/getCurrentMenus',
    data,
    includeCredentials: true,
    tokenProvider,
  });

// 修改密码

export const modifyPassword = (data, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<modifyData>>({
    method: 'POST',
    path: '/life-platform-dashboard/plat-user/modifyPassword',
    data,
    includeCredentials: true,
    tokenProvider,
  });
