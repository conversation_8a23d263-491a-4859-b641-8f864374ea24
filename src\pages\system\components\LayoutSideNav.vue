<template>
  <side-nav :menu="sideMenu" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { usePermissionStore } from '@/store';
import SideNav from './SideNav.vue';
import { getRootNameEnFromPath } from '@/router';

const route = useRoute();
const permissionStore = usePermissionStore();
const { routers: menuRouters } = storeToRefs(permissionStore);

// 将currentNameEn改为计算属性，使其能够响应路由变化
const currentNameEn = computed(() => {
  const { path } = route;
  // 使用router中的工具方法获取根路由的nameEn
  return getRootNameEnFromPath(path);
});

const sideMenu = computed(() => {
  const menuAuthListStr = localStorage.getItem('menuList');
  if (!menuAuthListStr) {
    console.warn('menuList 不存在于 localStorage 中');
    return [];
  }

  let menuAuthList;
  try {
    menuAuthList = JSON.parse(menuAuthListStr);
  } catch (error) {
    console.error('解析 menuList 失败:', error);
    return [];
  }

  console.log(menuAuthList, '查看数据');
  console.log(Array.isArray(menuAuthList), '判断类型');

  let navArr = [];
  if (Array.isArray(menuAuthList)) {
    for (const i of menuAuthList) {
      if (i.nameEn === currentNameEn.value) {
        navArr = i.list || [];
        break; // 找到后立即退出循环
      }
    }
    console.log(navArr, '权限菜单123');
  } else {
    console.error('menuAuthList 不是一个数组');
    return [];
  }

  console.log(menuRouters.value, 'menuRouters.value;');

  const currentMenuRouter = menuRouters.value.find((item) => item.nameEn === currentNameEn.value);
  if (!currentMenuRouter) {
    console.warn('未找到对应的菜单路由:', currentNameEn.value);
    return [];
  }

  const arr = [];
  for (const v of navArr) {
    const childrenToUse = currentMenuRouter?.children || [];
    for (const s of childrenToUse) {
      if (v.nameEn === s.nameEn) {
        arr.push(s);
        break; // 找到后立即退出内层循环
      }
    }
  }

  // 调用函数
  const nameEnArray = getAllNameEn(navArr);

  // 调用函数，得到筛选后的数据
  const filteredData = filterDataByNameEn(arr, nameEnArray);
  console.log(filteredData, '最终的数据666');
  return filteredData;
});

// 递归函数：获取所有nameEn
const getAllNameEn = (data) => {
  if (!Array.isArray(data)) return [];

  const result = [];
  const visited = new Set(); // 防止无限递归

  const traverse = (items) => {
    items.forEach((item) => {
      if (!item || visited.has(item)) return; // 防止循环引用
      visited.add(item);

      // 添加当前项的 nameEn
      if (item.nameEn) {
        result.push(item.nameEn);
      }

      // 如果有子节点，递归遍历子节点
      if (Array.isArray(item.list) && item.list.length > 0) {
        traverse(item.list);
      }
    });
  };

  traverse(data);
  return result;
};

// 递归筛选节点
const filterDataByNameEn = (data, selectedNamesEn) => {
  if (!Array.isArray(data) || !Array.isArray(selectedNamesEn)) return [];

  return data
    .map((item) => {
      if (!item) return null;

      // 过滤出当前节点符合 nameEn 的项
      if (selectedNamesEn.includes(item.nameEn)) {
        const newItem = { ...item }; // 创建副本避免修改原对象

        // 如果有子节点，也需要递归处理
        if (newItem.children && newItem.children.length > 0) {
          newItem.children = filterDataByNameEn(newItem.children, selectedNamesEn);
        }
        return newItem;
      }
      return null;
    })
    .filter((item) => item !== null);
};
</script>
