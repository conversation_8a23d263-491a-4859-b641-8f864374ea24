.light {
  &.login-wrapper {
    background-color: white;
    // background-image: url('@/assets/assets-login-bg-white.png');
  }
}

.dark {
  &.login-wrapper {
    background-color: var(--td-bg-color-page);
    // background-image: url('@/assets/assets-login-bg-black.png');
  }
}
.logo-box {
  display: flex;
  justify-content: center;
  margin: 60px 0;
}
.logo {
  width: 300px;
}
.login-wrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: 100%;
  position: relative;
}

.login-container {
  margin: 20vh auto;
}

.title-container {
  .title {
    font-size: 36px;
    line-height: 44px;
    color: var(--td-text-color-primary);
    margin-top: 4px;

    &.margin-no {
      margin-top: 0;
    }
  }

  .sub-title {
    margin-top: 16px;

    .tip {
      display: inline-block;
      margin-right: 8px;
      font-size: 14px;

      &:first-child {
        color: var(--td-text-color-secondary);
      }

      &:last-child {
        color: var(--td-text-color-primary);
        cursor: pointer;
      }
    }
  }
}

.item-container {
  // width: 400px;
  margin-top: 48px;

  &.login-qrcode {
    .tip-container {
      width: 192px;
      margin-bottom: 16px;
      font-size: 14px;
      display: flex;
      justify-content: space-between;

      .tip {
        color: var(--td-text-color-primary);
      }

      .refresh {
        display: flex;
        align-items: center;
        color: var(--td-brand-color);

        .t-icon {
          font-size: 14px;
          margin-left: 4px;
        }

        &:hover {
          cursor: pointer;
        }
      }
    }

    .bottom-container {
      margin-top: 32px;
    }
  }

  &.login-phone {
    .bottom-container {
      margin-top: 66px;
    }
  }

  .check-container {
    display: flex;
    align-items: center;

    &.remember-pwd {
      margin-bottom: 16px;
      justify-content: space-between;
    }

    :deep(.t-checkbox__label) {
      color: var(--td-text-color-secondary);
    }

    span {
      color: var(--td-brand-color);

      &:hover {
        cursor: pointer;
      }
    }
  }

  .verification-code {
    display: flex;
    align-items: center;

    :deep(.t-form__controls) {
      width: 100%;

      button {
        flex-shrink: 0;
        width: 102px;
        height: 40px;
        margin-left: 11px;
      }
    }
  }

  // .btn-container {
  //   margin-top: 48px;
  // }
}

.switch-container {
  margin-top: 24px;

  .tip {
    font-size: 14px;
    color: var(--td-brand-color);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    margin-right: 14px;

    &:last-child {
      &::after {
        display: none;
      }
    }

    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 12px;
      background: var(--td-gray-color-3);
      margin-left: 14px;
    }
  }
}

.check-container {
  font-size: 14px;
  color: var(--td-text-color-secondary);

  .tip {
    float: right;
    font-size: 14px;
    color: var(--td-brand-color);
  }
}

.copyright {
  font-size: 14px;
  position: absolute;
  left: 5%;
  bottom: 64px;
  color: var(--td-text-color-secondary);
}

@media screen and (max-height: 700px) {
  .copyright {
    display: none;
  }
}
