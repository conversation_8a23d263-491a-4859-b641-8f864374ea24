<template>
  <div>
    <t-layout key="side" :class="mainLayoutCls">
      <t-aside><layout-side-nav /></t-aside>
      <t-layout style="background: linear-gradient(to bottom, #d5efff, #ece4fc, #edf7fd)">
        <!-- <l-breadcrumb style="background: none;margin-top:10px;"/> -->
        <t-content>
          <noPage v-if="path === '/system/index' || path === '/systemManagement/index'" />
          <router-view v-else> </router-view>
        </t-content>
      </t-layout>
    </t-layout>
    <!-- <setting-com /> -->
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useSettingStore } from '@/store';

// import SettingCom from './setting.vue';
import LayoutSideNav from './components/LayoutSideNav.vue';
import noPage from '@/components/noPage.vue';
import router from '@/router';

import '@/style/layout.less';

const route = useRoute();
const path = ref('');
const settingStore = useSettingStore();

const mainLayoutCls = computed(() => [
  {
    't-layout--with-sider': settingStore.showSidebar,
  },
]);

watch(
  route, // 监听 route 对象
  (newRoute, oldRoute) => {
    console.log('路由变化了', newRoute, oldRoute);
    path.value = router.options.history.location;
    debugger;
  },
  { immediate: true, deep: true }, // 初始化时立即执行一次
);

// onMounted(() => {
//   appendNewRoute();
// });
</script>

<style lang="less" scoped>
.tdesign-starter-header-menu-fixed-side {
  z-index: 100 !important;
}
</style>
