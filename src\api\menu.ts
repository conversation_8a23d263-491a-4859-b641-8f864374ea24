import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';
import { Response } from './common';

/**
 * 商家菜单列表
 * @api-url:/merchant-menu/page
 * @params:
 * {
 *     page?: string;: 当前页码, 从 1 开始
 *    size?: string;每页显示条数, 每页显示条数, 最大值为 100
 *    sort?: string[];排序规则，格式为：property(,asc|desc)。默认为升序。支持传入多个排序字段。
 *
 * }
 * */
export const getMenuListService = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `/life-platform-dashboard/merchant-menu/page?page=${params.page}&size=${params.size}`,
    data: params,
    tokenProvider,
  });


/**
 * 删除菜单
 * @api-url:/merchant-menu/page
 * @params:
 * {
 *     page?: string;: 当前页码, 从 1 开始
 *    size?: string;每页显示条数, 每页显示条数, 最大值为 100
 *    sort?: string[];排序规则，格式为：property(,asc|desc)。默认为升序。支持传入多个排序字段。
 *
 * }
 * */
export const delMenuByIdService = (data, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: '/life-platform-dashboard/merchant-menu/del',
    data,
    includeCredentials: true,
    tokenProvider,
  });


interface IMenu {
  /**
   * 是否隐藏 0否 1是
   */
  hidden?: number;
  id?: number;
  /**
   * 菜单名称
   */
  menuName: string;
  /**
   * 父级ID 上级菜单
   */
  parentId?: number;
  /**
   * 权限CODE
   */
  permissionCode: string;
  /**
   * 权限级别：1、店铺管理员，2、员工
   */
  permissionLevel?: number;
  /**
   * 菜单类型 （0菜单 1按钮）
   */
  type?: number;
  /**
   * 使用分端：1、老商家pc端，2、新商家pc端，3、小掌柜小程序端，4、小掌柜app端
   */
  useScopeList?: number[];
  [property: string]: any;
}
/**
 * 新增菜单
 * @api-url:/merchant-menu/save
 * @params:
 * {
 *     page?: string;: 当前页码, 从 1 开始
 *    size?: string;每页显示条数, 每页显示条数, 最大值为 100
 *    sort?: string[];排序规则，格式为：property(,asc|desc)。默认为升序。支持传入多个排序字段。
 *
 * }
 * */
export const addMenuService = (data:IMenu, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: '/life-platform-dashboard/merchant-menu/save',
    data,
    includeCredentials: true,
    tokenProvider,
  });
