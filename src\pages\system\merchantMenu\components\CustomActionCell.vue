<!-- CustomActionCell.vue -->
<template>
  <Space>
    <Button type="link" @click="handleEdit">编辑</Button>
    <Button type="link" danger @click="handleDelete">删除</Button>
  </Space>
</template>

<script setup lang="ts">
import { defineProps,defineEmits } from 'vue';
import { Button, Modal, Space, message } from 'woody-ui';
import { delMenuByIdService } from '@/api/menu';

const props = defineProps<{
  record: any;
  onEdit: (record: any) => void;
  onDelete: (record: any) => void;
}>();

const handleEdit = () => {
  props.onEdit(props.record);
};

const handleDelete = () => {
  Modal.confirm({
    title: '你确认要删除吗？',
    onOk() {
      const {id}=props.record;
      if(!id){
        message.error('id为空，数据异常;请刷新后再试！');
        return
      }
       delMenuByIdService({id}).then((res)=>{
        const { code, message:msg } = res;
        if (code === 0) {
            message.success('删除成功');
            props.onDelete(true)
          return;
         }
        message.info(msg);
         props.onDelete(false)
       })
    },
    onCancel() {
      console.log('Cancel');
    },
    // class: 'test',
  });
  console.log('btn Delete record:', props.record);
};
</script>
