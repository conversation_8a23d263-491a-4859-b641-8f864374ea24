const menuArray = [
  {
    parent_id: '',
    permission_code: 'settled',
    type: 0,
    menu_name: '入驻',
    children: [
      {
        parent_id: '',
        permission_code: 'shopCenter',
        type: 0,
        menu_name: '店铺中心',
        children: [
          {
            parent_id: '',
            permission_code: 'onlineIncomingManagement',
            type: 0,
            menu_name: '线上进件管理',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'onlineShopManagement',
            type: 0,
            menu_name: '线上店铺管理',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'shop_onlineEcommerce',
            type: 0,
            menu_name: '线上电商',
            children: [
              {
                parent_id: '',
                permission_code: 'supplyWarehouseManagement',
                type: 0,
                menu_name: '供货仓管理',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'apiShopManagement',
                type: 0,
                menu_name: 'API店铺管理',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'shop_localLife',
            type: 0,
            menu_name: '本地生活',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'channelShopList',
            type: 0,
            menu_name: '渠道店铺列表',
            children: [],
          },
        ],
      },
      {
        parent_id: '',
        permission_code: 'supplyManagement',
        type: 0,
        menu_name: '供应链管理',
        children: [
          {
            parent_id: '',
            permission_code: 'supply_onlineEcommerce',
            type: 0,
            menu_name: '线上电商',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'supply_localLife',
            type: 0,
            menu_name: '本地生活',
            children: [],
          },
        ],
      },
    ],
  },
  {
    parent_id: '',
    permission_code: 'market',
    type: 0,
    menu_name: '营销',
    children: [
      {
        parent_id: '',
        permission_code: 'goodsCenter',
        type: 0,
        menu_name: '商品中心',
        children: [
          {
            parent_id: '',
            permission_code: 'goods_onlineEcommerce',
            type: 0,
            menu_name: '线上电商',
            children: [
              {
                parent_id: '',
                permission_code: 'goodsList',
                type: 0,
                menu_name: '商品列表',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'operationalShelvingReview',
                type: 0,
                menu_name: '运营上架审核',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'operationModificationReview',
                type: 0,
                menu_name: '运营修改审核',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'operationalDelistingReview',
                type: 0,
                menu_name: '运营下架审核',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'backendClassification',
                type: 0,
                menu_name: '后台分类',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'brandManagement',
                type: 0,
                menu_name: '品牌管理',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'specificationManagement',
                type: 0,
                menu_name: '规格管理',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'riskControlAudit',
            type: 0,
            menu_name: '风控审核',
            children: [
              {
                parent_id: '',
                permission_code: 'systemReview',
                type: 0,
                menu_name: '系统审核',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'marketReview',
                type: 0,
                menu_name: '营销审核',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'goods_riskControlAudit',
                type: 0,
                menu_name: '风控审核',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'goods_localLife',
            type: 0,
            menu_name: '本地生活',
            children: [],
          },
        ],
      },
      {
        parent_id: '',
        permission_code: 'cmsManagement',
        type: 0,
        menu_name: 'CMS管理',
        children: [
          {
            parent_id: '',
            permission_code: 'pageConfig',
            type: 0,
            menu_name: '页面配置',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'searchConfig',
            type: 0,
            menu_name: '搜索配置',
            children: [
              {
                parent_id: '',
                permission_code: 'searchPageConfig',
                type: 0,
                menu_name: '搜索页面配置',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'searchForScrollingPhrases',
                type: 0,
                menu_name: '搜索滚动词组',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'frontEndClassificationConfig',
            type: 0,
            menu_name: '前台分类配置',
            children: [
              {
                parent_id: '',
                permission_code: 'frontEndClassification',
                type: 0,
                menu_name: '前台分类',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'batchAddClassifiedProducts',
                type: 0,
                menu_name: '批量添加分类商品',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'materialCenter',
            type: 0,
            menu_name: '素材中心',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'localAreaDelivery',
            type: 0,
            menu_name: '同城专区配送',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'textureConfig',
            type: 0,
            menu_name: '贴图配置',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'productInfoConfig',
            type: 0,
            menu_name: '商品信息配置',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'loveInventoryClassifyManagement',
            type: 0,
            menu_name: '爱库存分类管理',
            children: [],
          },
        ],
      },
      {
        parent_id: '',
        permission_code: 'activityCenter',
        type: 0,
        menu_name: '活动中心',
        children: [
          {
            parent_id: '',
            permission_code: 'goodsShare',
            type: 0,
            menu_name: '商品分享',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'platformFullReduction',
            type: 0,
            menu_name: '平台满减',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'oneYuanZone',
            type: 0,
            menu_name: '一元专区',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'timeLimitedFlashSale',
            type: 0,
            menu_name: '限时秒杀',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'newbornZone',
            type: 0,
            menu_name: '新人专区',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'goldCoinLottery',
            type: 0,
            menu_name: '金币抽奖',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'selectionOfficer',
            type: 0,
            menu_name: '选品官',
            children: [],
          },
        ],
      },
      {
        parent_id: '',
        permission_code: 'mallManagement',
        type: 0,
        menu_name: '商城管理',
        children: [
          {
            parent_id: '',
            permission_code: 'shoppCartOrder',
            type: 0,
            menu_name: '购物车下单限制',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'supplyWarehouse',
            type: 0,
            menu_name: '供货仓',
            children: [
              {
                parent_id: '',
                permission_code: 'freightSubsidy',
                type: 0,
                menu_name: '运费补贴',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'goldCoinMall',
            type: 0,
            menu_name: '金币商城',
            children: [
              {
                parent_id: '',
                permission_code: 'goldCoinManagement',
                type: 0,
                menu_name: '金币管理',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'lifeCircle',
            type: 0,
            menu_name: '生活圈',
            children: [],
          },
        ],
      },
    ],
  },
  {
    parent_id: '',
    permission_code: 'trade',
    type: 0,
    menu_name: '交易',
    children: [
      {
        parent_id: '',
        permission_code: 'orderCenter',
        type: 0,
        menu_name: '订单中心',
        children: [
          {
            parent_id: '',
            permission_code: 'order_onlineEcommerce',
            type: 0,
            menu_name: '线上电商',
            children: [
              {
                parent_id: '',
                permission_code: 'mainOrderList',
                type: 0,
                menu_name: '主订单列表',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'sonOrderList',
                type: 0,
                menu_name: '子订单列表',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'order_localLife',
            type: 0,
            menu_name: '本地生活',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'channelOrderList',
            type: 0,
            menu_name: '渠道订单列表',
            children: [],
          },
        ],
      },
      {
        parent_id: '',
        permission_code: 'afterSalesCenter',
        type: 0,
        menu_name: '售后中心',
        children: [
          {
            parent_id: '',
            permission_code: 'afterSales_onlineEcommerce',
            type: 0,
            menu_name: '线上电商',
            children: [
              {
                parent_id: '',
                permission_code: 'userApplyAfterSales',
                type: 0,
                menu_name: '用户申请售后',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'platformOperationRefund',
                type: 0,
                menu_name: '平台操作退款',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'merchantCooperationRefund',
                type: 0,
                menu_name: '商家合作退款',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'afterSalesConfig',
                type: 0,
                menu_name: '售后配置',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'afterSales_localLife',
            type: 0,
            menu_name: '本地生活',
            children: [],
          },
        ],
      },
      {
        parent_id: '',
        permission_code: 'financialCenter',
        type: 0,
        menu_name: '财务中心',
        children: [
          {
            parent_id: '',
            permission_code: 'fundAccount',
            type: 0,
            menu_name: '资金账户',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'invoicesManagement',
            type: 0,
            menu_name: '发票管理',
            children: [],
          },
        ],
      },
    ],
  },
  {
    parent_id: '',
    permission_code: 'user',
    type: 0,
    menu_name: '用户',
    children: [
      {
        parent_id: '',
        permission_code: 'userCenter',
        type: 0,
        menu_name: '用户中心',
        children: [
          {
            parent_id: '',
            permission_code: 'memberManagement',
            type: 0,
            menu_name: '会员管理',
            children: [],
          },
          {
            parent_id: '',
            permission_code: 'trendyCustomerManangement',
            type: 0,
            menu_name: '潮客管理',
            children: [],
          },
        ],
      },
      {
        parent_id: '',
        permission_code: 'evaluateCenter',
        type: 0,
        menu_name: '评价中心',
        children: [],
      },
    ],
  },
  {
    parent_id: '',
    permission_code: 'data',
    type: 0,
    menu_name: '数据',
    children: [
      {
        parent_id: '',
        permission_code: 'dataCenter',
        type: 0,
        menu_name: '数据中心',
        children: [],
      },
    ],
  },
  {
    parent_id: '',
    permission_code: 'system',
    type: 0,
    menu_name: '系统',
    children: [
      {
        parent_id: '',
        permission_code: 'systemManagement',
        type: 0,
        menu_name: '系统管理',
        children: [
          {
            parent_id: '',
            permission_code: 'systemSetting',
            type: 0,
            menu_name: '权限',
            children: [
              {
                parent_id: '',
                permission_code: 'menuManangment',
                type: 0,
                menu_name: '菜单管理',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'adminManangment',
            type: 0,
            menu_name: '管理员管理',
            children: [
              {
                parent_id: '',
                permission_code: 'countManangment',
                type: 0,
                menu_name: '账号管理',
                children: [],
              },
              {
                parent_id: '',
                permission_code: 'roleManangment',
                type: 0,
                menu_name: '角色管理',
                children: [],
              },
            ],
          },
          {
            parent_id: '',
            permission_code: 'teamManangment',
            type: 0,
            menu_name: '团队管理',
            children: [
              {
                parent_id: '',
                permission_code: 'teamList',
                type: 0,
                menu_name: '团队列表',
                children: [],
              },
            ],
          },
        ],
      },
    ],
  },
];
