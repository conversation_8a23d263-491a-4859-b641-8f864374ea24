<template>
  <div class="material-center-container">
    <div class="left-part">
      <div class="title">
        <div>文件目录</div>
        <div>
          <span @click="showModal('ADD')">
            <t-icon size="15" style="margin-right: 3px" name="add" />
            <span>分组</span>
          </span>
        </div>
      </div>
      <div class="tree-container">
        <div :class="['un-group', ownerGroupId === '0' ? 'active' : '']" @click="treeClick({ value: '0' })">
          <div>未分组</div>
          <div>{{ countInfo }}</div>
        </div>
        <div class="real-tree">
          <t-tree
            :data="treeData"
            :actived="[ownerGroupId]"
            :keys="{ value: 'id', label: 'name' }"
            activable
            hover
            transition
          >
            <template #label="{ node }">
              <span>
                <span v-if="isEdit.isEdit && isEdit.id === node.value" class="edit-content">
                  <t-input v-model="currentNode" maxlength="10" />
                  <span class="check" @click="save(node)">
                    <t-icon size="18" name="check" />
                  </span>
                </span>
                <span v-else class="label-info" @click="treeClick(node)">
                  <span>{{ node.data.name }}</span>
                  <span style="margin-right: 30px">{{ node.data.fileCnt }}</span>
                  <span class="edit-icon" @click.stop="showInput(node)">
                    <t-icon size="15" name="edit" />
                  </span>
                </span>
              </span>
            </template>
          </t-tree>
        </div>
      </div>
    </div>
    <div class="right-part">
      <div class="title">
        <div>
          <t-checkbox
            :checked="checkedInfo.checkAll"
            :indeterminate="checkedInfo.indeterminate"
            :on-change="handleSelectAll"
            >已选{{ checkedInfo.count }}项</t-checkbox
          >
        </div>
        <div class="upload-search">
          <t-upload
            :trigger-button-props="{ content: '上传图片', theme: 'primary', variant: 'base' }"
            @click.prevent="showModal('UPLOAD')"
          />
          <t-input v-model="fileName" placeholder="请输入图片名称" @enter="searchPic">
            <template #prefixIcon>
              <t-icon name="search" :style="{ cursor: 'pointer' }" @click="searchPic" />
            </template>
          </t-input>
        </div>
      </div>
      <div class="operate-group">
        <span @click="showModal('MODIFY')">修改分组</span>
        <!-- <span @click="showModal($event, 'DELETE')">删除</span> -->
        <span @click="download">批量下载</span>
      </div>
      <div v-if="picList.length" class="pic-container">
        <t-checkbox-group v-model="checkedInfo.checkedList" @change="checkedListChange">
          <div v-for="(item, index) in picList" :key="index" class="pic-list">
            <div class="img-container">
              <img class="logo" :src="item.url" alt="logo" />
              <div class="bg-hover">
                <span @click="showModal('EDIT', item)">编辑</span>
                <!-- <span @click="showModal($event, 'DELETE')">删除</span> -->
              </div>
            </div>
            <div style="margin-top: 12px; width: 100%">
              <t-checkbox :key="item.url" :value="item.id" :title="item.name">{{ item.name }}</t-checkbox>
            </div>
          </div>
        </t-checkbox-group>
      </div>
      <div v-else style="margin-top: 100px; text-align: center" class="pic-container">
        <no-data />
      </div>
      <div class="page-container">
        <t-pagination
          v-model="pageInfo.current"
          :total="pageInfo.total"
          :page-size="pageInfo.pageSize"
          show-jumper
          @change="pageChange"
        />
      </div>
    </div>
    <modalComp
      :modal-info="modalInfo"
      :group-option="treeData"
      @confirm="confirmFunc"
      @cancel="cancelFunc"
      @query-group-func="queryGroupFunc"
    />
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import modalComp from './modalComp.vue';
import noData from '@/components/Nodata/index.vue';
import { queryGroup, queryByPage, countQuery, saveGroup } from '@/api/resources';
import { downImg } from '@/utils/zip';

const route = useRoute();
const currentNode = ref();
const fileName = ref('');
const treeData = ref([]);
const ownerGroupId = ref('0');
const countInfo = ref();
const picList = ref([]);
const isEdit = reactive({
  isEdit: false,
  id: 0,
});
const pageInfo = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});
const checkedInfo = reactive({
  checkAll: false,
  indeterminate: false,
  count: 0,
  checkedList: [],
});
const modalInfo = reactive({
  type: '',
  showModal: false,
  checkList: [],
  formInfo: {},
});

onMounted(() => {
  queryGroupFunc();
  querCount();
  queryMaterial();
  if (route.query.modalType) {
    showModal(route.query.modalType);
  }
});

// 查询分组数据
const queryGroupFunc = () => {
  queryGroup({ flatFlag: false }).then((res) => {
    if (res.code === 0) {
      treeData.value = res.data || [];
    }
  });
};

// 查询未分组数量
const querCount = () => {
  countQuery({ groupId: 0 }).then((res) => {
    if (res.code === 0) {
      countInfo.value = res.data;
    }
  });
};

//  点击当前树
const treeClick = (node) => {
  ownerGroupId.value = node.value;
  queryMaterial({
    ownerGroupId: node.value,
    page: 1,
    size: 10,
  });
  initPage();
};

// 查询当前素材
const queryMaterial = (param) => {
  const prams = param || { ownerGroupId: 0, page: 1, size: 10 };
  queryByPage(prams).then((res) => {
    if (res.code === 0) {
      picList.value = res.data.records;
      pageInfo.total = res.data.total;
    }
  });
};
// 全选
const handleSelectAll = (e) => {
  checkedInfo.checkAll = !checkedInfo.checkAll;
  if (e) {
    checkedInfo.count = picList.value.length;
    const arr = [];
    picList.value.forEach((item) => {
      arr.push(item.id);
    });
    checkedInfo.checkedList = arr;
    checkedInfo.indeterminate = false;
  } else {
    checkedInfo.count = 0;
    checkedInfo.checkedList = [];
  }
};
// 单独勾选
const checkedListChange = (e) => {
  checkedInfo.count = e.length;
  checkedInfo.checkedList = e;
  if (e.length > 0) {
    if (e.length === picList.value.length) {
      checkedInfo.checkAll = true;
      checkedInfo.indeterminate = false;
    } else {
      checkedInfo.checkAll = false;
      checkedInfo.indeterminate = true;
    }
  } else {
    checkedInfo.checkAll = false;
    checkedInfo.indeterminate = false;
  }
};
// 编辑树
const showInput = (node) => {
  isEdit.isEdit = true;
  isEdit.id = node.data.id;
  currentNode.value = node.label;
};
// 保存修改
const save = (node) => {
  // 保存分组名称修改
  saveGroup({
    id: node.value,
    name: currentNode.value,
    parentId: node.data.parentId,
    level: node.data.level,
  }).then((res) => {
    if (res.code === 0) {
      MessagePlugin.success('保存成功！');
      queryGroupFunc();
    }
  });
  isEdit.isEdit = false;
  isEdit.id = 0;
};

// 批量下载
const download = () => {
  if (checkedInfo.checkedList.length === 0) {
    MessagePlugin.warning('请先选择素材！');
    return;
  }
  const url = picList.value
    .filter((item) => checkedInfo.checkedList.includes(item.id))
    .map((item) => ({
      url: item.url,
      title: item.name,
    }));

  downImg(url, () => {
    MessagePlugin.success('下载完成');
    initCheck();
  });
};

// 弹框
const showModal = (type, formInfo) => {
  if (type === 'MODIFY' && checkedInfo.checkedList.length === 0) {
    MessagePlugin.warning('请先选择素材！');
    return;
  }
  modalInfo.showModal = true;
  modalInfo.type = type;
  modalInfo.checkList = checkedInfo.checkedList;
  if (formInfo) {
    modalInfo.formInfo = formInfo;
  } else {
    modalInfo.formInfo = {};
  }
};
// 隐藏弹框
const cancelFunc = () => {
  modalInfo.showModal = false;
  modalInfo.type = '';
};
// 确认弹框
const confirmFunc = () => {
  modalInfo.showModal = false;
  modalInfo.type = '';
  queryGroupFunc();
  querCount();
  queryMaterial({
    ownerGroupId: ownerGroupId.value,
    page: 1,
    size: 10,
  });
  initPage();
  initCheck();
};
const initCheck = () => {
  checkedInfo.checkedList = [];
  checkedInfo.indeterminate = false;
  checkedInfo.checkAll = false;
  checkedInfo.count = 0;
};
// 分页切换
const pageChange = ({ current, pageSize }) => {
  pageInfo.current = current;
  pageInfo.pageSize = pageSize;
  queryMaterial({
    fileName: fileName.value,
    ownerGroupId: ownerGroupId.value,
    page: current,
    size: pageSize,
  });
  initCheck();
};

// 图片搜索
const searchPic = () => {
  queryMaterial({
    fileName: fileName.value,
    ownerGroupId: ownerGroupId.value,
    page: 1,
    size: 10,
  });
  initPage();
};

// 初始化分页数据
const initPage = () => {
  pageInfo.current = 1;
  pageInfo.pageSize = 10;
};
</script>

<style lang="less" scoped>
@import url('./style.less');
</style>
