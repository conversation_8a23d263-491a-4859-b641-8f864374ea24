<template>
  <div class="login-wrapper">
    <div class="login-container">
      <div class="title-container">
        <div class="logo-box">
          <img class="logo" src="@/assets/images/big-logo1.png" alt="logo" />
        </div>
        <login />
      </div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'LoginIndex',
};
</script>
<script setup lang="ts">
import Login from './components/Login.vue';
</script>

<style lang="less" scoped>
@import url('./index.less');
</style>
