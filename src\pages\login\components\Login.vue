<template>
  <a-form ref="formRef" :model="formData" :rules="FORM_RULES">
    <a-form-item name="userName">
      <a-input v-model:value="formData.userName" class="f-itm" placeholder="请输入账号">
        <template #prefix>
          <user-outlined />
        </template>
      </a-input>
    </a-form-item>

    <a-form-item name="password">
      <a-input-password v-model:value="formData.password" class="f-itm" clearable placeholder="请输入密码">
        <template #prefix>
          <lock-outlined />
        </template>
      </a-input-password>
    </a-form-item>
    <yidun-captcha class="code-box" :refresh-code="refreshCode" @get-code="getCode" />

    <a-form-item v-if="type !== 'qrcode'">
      <a-button class="f-itm" style="height: 40px" type="primary" :loading="loginLoading" @click="onSubmit">
        登录
      </a-button>
    </a-form-item>
  </a-form>
  <a-modal
    title="修改密码"
    width="35%"
    :close-on-overlay-click="true"
    :visible="pdVisible"
    :on-close="onPdClose"
    :destroy-on-close="true"
    @ok="pdSubmit"
    @cancel="pdVisible = false"
  >
    <div class="dialog-pad">
      <a-form
        ref="formEditRef"
        :model="loginData"
        :rules="PASSWORD_RULES"
        label-align="right"
        :label-col="{ style: { width: '100px' } }"
      >
        <p class="title-css">您当前密码为初始密码，请修改后才可以登录</p>
        <a-form-item label="账号" label-width="100px">
          <span>{{ formData.userName }}</span>
        </a-form-item>
        <a-form-item label="输入密码" name="password">
          <a-input-password
            v-model:value="loginData.password"
            type="password"
            :maxlength="30"
            show-count
            placeholder="请输入密码"
          >
            <template #prefix>
              <lock-on-icon />
            </template>
          </a-input-password>
        </a-form-item>
        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password
            v-model:value="loginData.confirmPassword"
            type="password"
            :maxlength="30"
            show-count
            placeholder="请再次输入密码"
          >
            <template #prefix-icon>
              <lock-on-icon />
            </template>
          </a-input-password>
        </a-form-item>
        <p class="title-red">限制8-30个字符，必须包含英文大小写以及数字，不包含中文以及中文符号</p>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'woody-ui';
import type { FormInstanceFunctions, FormRule } from 'tdesign-vue-next';
import microApp from '@micro-zoe/micro-app';
import { useUserStore } from '@/store';
import { modifyPassword, getCurrentMenus } from '@/api/login';
import { useNavStore } from '@/store/modules/navStore'; // 引入store
import { validatePassword } from '@/utils/common';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
import YidunCaptcha from './YidunCaptcha.vue';

const pCode = ref(''); // turnstile code
const refreshCode = ref(0); // 判断是否要刷新验证码

const userStore = useUserStore();
const pdVisible = ref(false);
const formRef = ref();
const formEditRef = ref();
const INITIAL_DATA = {
  userName: '',
  password: '',
};

const PASSWORD_DATA = {
  confirmPassword: '',
  password: '',
};

const FORM_RULES: Record<string, FormRule[]> = {
  userName: [{ required: true, message: '账号必填', trigger: 'blur' }],
  password: [{ required: true, message: '密码必填', trigger: 'blur' }],
};

const PASSWORD_RULES: Record<string, FormRule[]> = {
  confirmPassword: [{ required: true, message: '确认密码必填', trigger: 'blur' }],
  password: [{ required: true, message: '密码必填', trigger: 'blur' }],
};

const type = ref('password');
const formData = ref({ ...INITIAL_DATA });
const loginData = reactive({ ...PASSWORD_DATA });
const tabArr = ref([]);
const oldMeunData = ref([]);
const loginLoading = ref(false);

const router = useRouter();
const route = useRoute();

// 获取验证码
const getCode = (code) => {
  pCode.value = code;
};

const onSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      if (loginLoading.value) return;
      try {
        if (!pCode.value) {
          message.error('请点击验证');
          return;
        }
        loginLoading.value = true;

        const res = await userStore.login({ ...formData.value, validate: pCode.value });
        if (res.code === 0) {
          console.log(res, 'res23456');
          localStorage.setItem('user-auth', JSON.stringify(res.data));
          loginLoading.value = false;
          setTimeout(() => {
            // 获取菜单
            getMenuList(res.data.defaultPassword);
          }, 50);
        } else {
          refreshCode.value = Math.random();
          loginLoading.value = false;
        }
      } catch (e) {
        refreshCode.value = Math.random();
        pCode.value = '';
        loginLoading.value = false;
      }
    })
    .catch((error) => {
      console.log('error', error);
    });
};

// 获取当前用户权限菜单数据
const getMenuList = async (isEditPW) => {
  tabArr.value = []; // 获取菜单前先清空
  const res = await getCurrentMenus({});
  if (res.code === 0) {
    const { menuList } = res.data;
    for (const i of menuList) {
      if (i.clientType === 2) {
        tabArr.value.push(i);
      } else if (i.clientType === 1) {
        // 老平台端单独处理的逻辑
        oldMeunData.value.push(i);
      }
    }
    // 2025.5.28 先隐藏老平台端菜单
    // if (oldMeunData.value.length) {
    //   // 集成老平台端的逻辑，在导航倒数第一的前面插入老平台端tab
    //   const obj = {
    //     name: '老平台端',
    //     nameEn: 'npOldPlatform',
    //     list: oldMeunData,
    //   };
    //   tabArr.value.splice(-1, 0, obj);
    // }

    localStorage.setItem('menuList', JSON.stringify(tabArr.value));
    console.log(tabArr, '最终的菜单数据');
    // 获取 store 实例并存入数据
    const navStore = useNavStore();
    navStore.setTabArr(tabArr); // 存储到store中
    // 发送数据给子应用 my-app，setData第二个参数只接受对象类型
    microApp.setData('my-app', { tabArr, oldMeunData });
    // 需要弹出修改密码弹出框
    if (isEditPW) {
      loginData.password = '';
      loginData.confirmPassword = '';
      pdVisible.value = true;
      loginLoading.value = false;
    } else {
      goToUrl();
    }
  } else {
    message.error(res.message);
    refreshCode.value = Math.random();
  }
};

// 登录后跳转逻辑
const goToUrl = () => {
  const redirect = route.query.redirect as string;
  // const redirectUrl = redirect ? decodeURIComponent(redirect) : '/';
  const projectName = route.query.project;
  if (redirect && projectName) {
    // 同项目之前token缺失/过期
    // path: redirect ? `/${projectName}/index?${projectName}=${redirectUrl}` : '/',
    router.push({
      path: redirect ? `/${projectName}/index` : '/',
    });
  } else if (redirect && !projectName) {
    // 不同子应用之间token缺失/过期的跳转逻辑
    // path: redirect ? `${redirectUrl}` : '/',
    router.push({
      path: '/',
    });
  } else {
    // 初始化默认的
    router.push({
      path: '/',
    });
  }

  message.success('登录成功');
};

const onPdClose = () => {
  pdVisible.value = false;
};

// 修改密码
const pdSubmit = () => {
  formEditRef.value
    .validate()
    .then(async () => {
      const { password, confirmPassword } = loginData;
      if (password !== confirmPassword) {
        message.error('两次密码输入不一致，请重新输入');
        return;
      }
      if (!validatePassword(password).status) {
        message.error(validatePassword(password).message);
        return;
      }
      const res = await modifyPassword({ ...loginData });
      if (res.code === 0) {
        message.success('已修改，请重新登录');
        pdVisible.value = false;
        loginData.password = '';
        loginData.confirmPassword = '';
      } else {
        message.success(res.message || '系统错误，请稍后再试');
      }
    })
    .catch((error) => {
      console.log('error', error);
    });
};
onMounted(() => {
  // const script = document.createElement('script');
  // script.src = 'https://cstaticdun.126.net/load.min.js';
  // script.onload = initCaptcha;
  // script.onerror = () => {
  //   console.error('验证码脚本加载失败');
  // };
  // document.body.appendChild(script);
});
</script>

<style lang="less" scoped>
@import url('../index.less');

.tur-center {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}
.code-box {
  padding-bottom: 30px;
}
.f-itm {
  width: 300px;
  margin: 0 auto;
}
.title-css {
  text-align: center;
  line-height: 30px;
  margin-bottom: 30px;
}
.title-red {
  font-size: 12px;
  color: red;
  text-align: center;
  line-height: 30px;
  margin-top: 10px;
}
</style>
