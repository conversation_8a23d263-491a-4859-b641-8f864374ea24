<template>
  <Drawer
    :title="isEditing ? '编辑' : '新增'"
    :open="visible"
    :footer-style="{ textAlign: 'right' }"
    width="500"
    @close="handleCancel"
  >
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk">确认</a-button>
    </template>

    <Form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 24 }"
      :rules="rules"
      :layout="formLayout"
    >
      <Form.Item name="id">
        <Input type="hidden" v-model:value="formState.id" />
      </Form.Item>
      <Form.Item label="类型" name="type">
        <Radio.Group v-model:value="formState.type">
          <Radio :value="0">菜单</Radio>
          <Radio :value="1" disabled>按钮</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item label="菜单名称" name="menuName">
        <TextArea v-model:value="formState.menuName" show-count :maxlength="30" />
      </Form.Item>
      <Form.Item label="Code" name="permissionCode">
        <TextArea v-model:value="formState.permissionCode" show-count :maxlength="50" />
      </Form.Item>
      <Form.Item label="上级菜单" name="parentId">
        <tree-select
          v-model:value="formState.parentId"
          show-search
          :fieldNames="{ children: 'children', label: 'menuName', value: 'id' }"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择上级菜单"
          allow-clear
          tree-default-expand-all
          treeNodeLabelProp="menuName"
          tree-node-filter-prop="menuName"
          :tree-data="treeData"
        />
      </Form.Item>
      <Form.Item label="是否隐藏" name="hidden">
        <Radio.Group v-model:value="formState.hidden">
          <Radio :value="1">是</Radio>
          <Radio :value="0">否</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item label="菜单使用级别" name="permissionLevel">
        <Radio.Group v-model:value="formState.permissionLevel">
          <Radio :value="1">管理员</Radio>
          <Radio :value="2">全员</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item label="菜单使用范围" name="useScope">
        <Checkbox.Group v-model:value="formState.useScope">
          <Checkbox value="1" name="type">我店小掌柜小程序</Checkbox>
          <Checkbox value="2" name="type">我店小掌柜app</Checkbox>
          <Checkbox value="3" name="type">老商家端</Checkbox>
          <Checkbox value="4" name="type">新商家端</Checkbox>
          <Checkbox value="10" name="type">供应链端</Checkbox>
        </Checkbox.Group>
      </Form.Item>
    </Form>
  </Drawer>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, reactive, watch, ref } from 'vue';
import { Form, Input, Radio, TreeSelect, Drawer, Checkbox } from 'woody-ui';
const { TextArea } = Input;

const props = defineProps<{
  visible: boolean;
  record?: any;
  isEditing: boolean;
  menuList: TreeNode[];
}>();

const emit = defineEmits(['update:visible', 'submit']);
const formRef = ref<any>();
const formLayout = ref('vertical'); // 设置布局为垂直
const formState = reactive({
  menuName: '',
  type: 1, // 默认类型1 (菜单)
  parentId: undefined,
  hidden: 0, // 默认不隐藏
  permissionCode: '',
  useScope: ['1', '2'],
  permissionLevel: 2,
  id: undefined,
});

const treeData = ref<TreeNode[]>([]);

const rules: any = {
  menuName: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
    { max: 30, message: '菜单名称最多30个字符', trigger: 'blur' },
  ],
  permissionCode: [
    { required: true, message: '请输入Code', trigger: 'blur' },
    { max: 50, message: 'Code最多50个字符', trigger: 'blur' },
  ],
  useScope: [{ required: true, message: '请选择菜单使用范围', trigger: 'change', type: 'array' }],
};

watch(
  () => props.record,
  (newRecord) => {
    if (newRecord?.id) {
      formState.menuName = newRecord.menuName;
      formState.type = newRecord.type;
      formState.permissionCode = newRecord.permissionCode;
      formState.parentId = newRecord.parentId || null;
      formState.hidden = newRecord.hidden;
      formState.useScope = newRecord.useScopeList?.map(String); // 编辑的时候直接读取list(后端返回会返回数组和字符串)
      formState.permissionLevel = newRecord.permissionLevel;
      formState.id = newRecord.id;
    } else {
      formState.menuName = '';
      formState.type = 0;
      formState.permissionCode = '';
      formState.parentId = undefined;
      formState.hidden = 0;
      formState.useScope = ['1', '2'];
      formState.permissionLevel = 2;
    }
  },
  { immediate: true },
);

watch(
  () => props.menuList,
  (newMenuList) => {
    treeData.value = buildTree(newMenuList);
  },
  { immediate: true },
);

interface TreeNode {
  id: number;
  parentId: number;
  permissionCode: string;
  type: number;
  menuName: string;
  permissionLevel: number;
  createTime: string;
  updateTime: string;
  menuBusinessList: any;
  relBusinessTypeName: any;
  useScope: number[];
  hidden: number;
  children?: TreeNode[];
}

function buildTree(data: TreeNode[]): TreeNode[] {
  const map = new Map<number, TreeNode>();
  const tree: TreeNode[] = [];

  // 将所有节点放入 map 中，键为 id，值为节点对象
  data.forEach((item) => {
    map.set(item.id, { ...item });
  });

  // 遍历所有节点，根据 parentId 将子节点添加到父节点的 children 数组中
  data.forEach((item) => {
    const node = map.get(item.id);
    if (!node) {
      console.warn(`Node with id ${item.id} not found in map`);
      return;
    }

    if (item.parentId !== 0) {
      const parent = map.get(item.parentId);
      if (parent) {
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(node);
      } else {
        console.warn(`Parent with id ${item.parentId} not found for item with id ${item.id}`);
      }
    } else {
      tree.push(node);
    }
  });

  // 移除空的 children 字段
  const removeEmptyChildren = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      if (node.children && node.children.length === 0) {
        delete node.children;
      } else if (node.children) {
        removeEmptyChildren(node.children);
      }
    });
  };

  removeEmptyChildren(tree);

  return tree;
}

const handleOk = () => {
  formRef.value
    ?.validate()
    .then(() => {
      emit('submit', { ...formState });
      emit('update:visible', false);
      formState.id = undefined;
    })
    .catch(() => {
      // 校验失败
    });
};

const handleCancel = () => {
  emit('update:visible', false);
  formState.id = undefined;
};
</script>
