import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import uniq from 'lodash/uniq';

// 自动导入modules文件夹下所有ts文件
const modules = import.meta.globEager('./modules/**/*.ts');

// 路由暂存
const routeModuleList: Array<RouteRecordRaw> = [];

Object.keys(modules).forEach((key) => {
  const mod = modules[key].default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

// 关于单层路由，meta 中设置 { single: true } 即可为单层路由，{ hidden: true } 即可在侧边栏隐藏该路由

// 存放动态路由
export const asyncRouterList: Array<RouteRecordRaw> = [...routeModuleList];

// 存放固定的路由
const defaultRouterList: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/pages/login/index.vue'),
  },
  {
    path: '/',
    redirect: '/index/index',
  },
  {
    path: '/:w+',
    name: '404Page',
    redirect: '/result/404',
  },
];

export const allRoutes = [...defaultRouterList, ...asyncRouterList];

/**
 * 根据路径获取根路由的nameEn
 * @param path 当前路径
 * @returns 根路由的nameEn
 */
export const getRootNameEnFromPath = (path: string): string => {
  // 获取路径的第一级，如 /system/xxx 得到 /system
  const rootPath = `/${path.split('/')[1]}`;

  // 查找根路由
  const rootRoute = asyncRouterList.find((route) => route.path === rootPath);
  if (rootRoute && (rootRoute as any).nameEn) {
    return (rootRoute as any).nameEn;
  }

  // 处理特殊情况：子路由完整路径
  for (const route of asyncRouterList) {
    // 检查是否有子路由匹配当前路径
    if (route.children) {
      for (const child of route.children) {
        // 有些子路由的path是完整路径(以/开头)，需要直接匹配
        if (child.path === path && (route as any).nameEn) {
          return (route as any).nameEn;
        }

        // 处理子路由的子路由
        if ((child as any).children) {
          for (const grandChild of (child as any).children) {
            if (grandChild.path === path && (route as any).nameEn) {
              return (route as any).nameEn;
            }
          }
        }
      }
    }
  }

  return '';
};

export const getRoutesExpanded = () => {
  const expandedRoutes = [];

  allRoutes.forEach((item) => {
    if (item.meta && item.meta.expanded) {
      expandedRoutes.push(item.path);
    }
    if (item.children && item.children.length > 0) {
      item.children
        .filter((child) => child.meta && child.meta.expanded)
        .forEach((child: RouteRecordRaw) => {
          expandedRoutes.push(item.path);
          expandedRoutes.push(`${item.path}/${child.path}`);
        });
    }
  });
  return uniq(expandedRoutes);
};

export const getActive = (maxLevel: number, route: any): string => {
  if (!route || !route.path) {
    return '';
  }
  return route.path
    .split('/')
    .filter((_item: string, index: number) => index <= maxLevel && index > 0)
    .map((item: string) => `/${item}`)
    .join('');
};

const router = createRouter({
  history: createWebHashHistory(),
  routes: allRoutes,
  // scrollBehavior() {
  //   return {
  //     el: '#app',
  //     top: 0,
  //     behavior: 'smooth',
  //   };
  // },
});

export default router;
