<template>
  <div>
    <t-layout key="no-side">
      <t-header><layout-header /> </t-header>
      <!-- :class="mainLayoutCls" -->
      <t-layout>
        <router-view> </router-view>
      </t-layout>
    </t-layout>
    <!-- <setting-com /> -->
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
// import { storeToRefs } from 'pinia';
import { useSettingStore, useTabsRouterStore } from '@/store';
// import LBreadcrumb from './components/Breadcrumb.vue';

// import SettingCom from './setting.vue';
import LayoutHeader from './components/LayoutHeader.vue';
// import LayoutSideNav from './components/LayoutSideNav.vue';

import '@/style/layout.less';

const route = useRoute();
const settingStore = useSettingStore();
const tabsRouterStore = useTabsRouterStore();
// const setting = storeToRefs(settingStore);

const mainLayoutCls = computed(() => [
  {
    't-layout--with-sider': settingStore.showSidebar,
  },
]);

const appendNewRoute = () => {
  const {
    path,
    query,
    meta: { title },
    name,
  } = route;
  tabsRouterStore.appendTabRouterList({ path, query, title: title as string, name, isAlive: true, meta: route.meta });
};

onMounted(() => {
  appendNewRoute();
});

watch(
  () => route.path,
  () => {
    appendNewRoute();
    // document.querySelector(`.${prefix}-layout`).scrollTo({ top: 0, behavior: 'smooth' });
  },
);
</script>

<style lang="less" scoped>
.tdesign-starter-header-menu-fixed-side {
  z-index: 100 !important;
}
</style>
