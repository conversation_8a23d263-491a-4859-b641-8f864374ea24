<template>
  <a-config-provider :locale="locale" :theme="theme">
    <router-view :class="[mode]" />
  </a-config-provider>
</template>
<script setup lang="ts">
import { computed, onMounted } from 'vue';
import zhCN from 'woody-ui/es/locale/zh_CN';
import theme from '@/style/theme-all';
import { useSettingStore } from '@/store';
import { getCategoryDropDown } from './utils';

const store = useSettingStore();

const locale = zhCN;

const mode = computed(() => {
  return store.displayMode;
});

onMounted(async () => {
  getCategoryDropDown();
});
</script>
<style lang="less" scoped>
#nprogress .bar {
  background: var(--td-brand-color) !important;
}
</style>
