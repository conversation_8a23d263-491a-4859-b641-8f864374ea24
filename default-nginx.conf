server {
    listen 80;
    root /var/www/html/;
    location / {
        root   /var/www/html/;
        index index.html index.htm;
        # 如果请求来自.shwoody.com的子域名，则允许跨域
        if ($http_origin ~* https?://[^.]+\.shwoody\.com$) {
            add_header 'Access-Control-Allow-Origin' $http_origin;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
        }
    }
}