<template>
  <template v-for="(item, index) in navData" :key="index">
    <template v-if="!item.children || !item.children.length">
      <a-menu-item :key="item?.path">
        <template #icon>
          <icon-font
            v-if="item.meta.state == 'IS_SPOT'"
            type="icon-yuandian11"
            class="icon-boxs"
            style="color: inherit; font-size: 4px"
          />
        </template>
        {{ item?.meta?.title }}
      </a-menu-item>
    </template>
    <template v-else>
      <a-sub-menu :key="item?.path" :title="item?.meta?.title">
        <template #icon>
          <template v-if="item.meta.state == 'IS_SHOW'">
            <img v-if="iconArr.includes(item?.path)" :src="item?.meta?.activeIcon" class="icon-css" />
            <img v-if="!iconArr.includes(item?.path)" :src="item?.meta?.icon" class="icon-css" />
          </template>
          <template v-else>
            <icon-font type="icon-yuandian11" class="icon-boxs" style="color: inherit; font-size: 4px" />
          </template>
        </template>
        <template #expandIcon="{ isOpen }">
          <icon-font v-if="isOpen" type="icon-jiantou-shang1" style="font-size: 14px" />
          <icon-font v-else type="icon-jiantou-xia1" style="font-size: 14px" />
        </template>
        <menu-content
          v-if="item.children && currentDepth < 3"
          :nav-data="item.children"
          :icon-arr="iconArr"
          :current-depth="currentDepth + 1"
        />
      </a-sub-menu>
    </template>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { PropType } from 'vue';
import { createFromIconfontCN } from '@ant-design/icons-vue';
import type { MenuRoute } from '@/types/interface';

const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_4812706_1rmgdvjexol.js',
});

const props = defineProps({
  navData: {
    type: Array as PropType<MenuRoute[]>,
    default: () => [],
  },
  iconArr: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  currentDepth: {
    type: Number,
    default: 0,
  },
});

// 使用传入的深度参数
const currentDepth = computed(() => {
  return props.currentDepth;
});

// 添加默认导出
defineExpose({});
</script>
<style lang="less" scoped>
.icon-css {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.icon-boxs {
  margin-right: -5px !important;
  margin-left: 9px;
}

:deep(.ant-menu-item-selected) {
  background: #ffffff !important;
  border-radius: 4px !important;
  font-weight: bold;
}

:deep(.ant-menu-item) {
  height: 48px !important;
  line-height: 48px !important;
}

:deep(.ant-menu-submenu-title) {
  height: 48px !important;
  line-height: 48px !important;
}

:deep(.t-menu) {
  border-right: 0 !important;
}
</style>
