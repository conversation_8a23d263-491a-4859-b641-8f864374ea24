import { MessagePlugin } from 'tdesign-vue-next';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style

import { getPermissionStore, getUserStore } from '@/store';
import router from '@/router';

NProgress.configure({ showSpinner: false });

router.beforeEach(async (to, from, next) => {
  NProgress.start();

  const userAuth = JSON.parse(localStorage.getItem('user-auth')) || '';
  const accessToken = userAuth ? userAuth.accessToken : '';

  const userStore = getUserStore();
  const permissionStore = getPermissionStore();
  const whiteListRouters = ['/login'];

  if (accessToken) {
    if (to.path === '/login') {
      next();
      return;
    }
    const { roles } = userStore;
    if (roles && roles.length > 0) {
      permissionStore.initRoutes(roles);
      next();
    } else {
      try {
        const userInfo = await userStore.getUserInfo();
        permissionStore.initRoutes(userInfo.roles);
        if (router.hasRoute(to.name)) {
          next();
        } else {
          next(`/`);
        }
      } catch (error) {
        MessagePlugin.error(error);
        next({
          path: '/login',
          query: { redirect: encodeURIComponent(to.fullPath) },
        });
        NProgress.done();
      }
    }
  } else {
    // 没有accessToken时
    if (whiteListRouters.indexOf(to.path) !== -1) {
      next();
    } else {
      next({
        path: '/login',
        query: { redirect: encodeURIComponent(to.fullPath) },
      });
    }
    NProgress.done();
  }
});

router.afterEach((to) => {
  if (to.path === '/login') {
    const userStore = getUserStore();
    const permissionStore = getPermissionStore();

    userStore.logout();
    permissionStore.restore();
  }
  NProgress.done();
});
